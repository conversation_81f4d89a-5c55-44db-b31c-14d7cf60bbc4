import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../models/user_model.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_dialog_widget.dart';
import 'user_form_dialog.dart';
import 'user_permissions_dialog.dart';

/// شاشة إدارة المستخدمين المحسنة
class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> {
  final AdminController _adminController = Get.find<AdminController>();
  final UnifiedPermissionService _permissionService =
      Get.find<UnifiedPermissionService>();
  final TextEditingController _searchController = TextEditingController();
  final RxList<User> _filteredUsers = <User>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _selectedFilter = 'all'.obs;
  final RxString _selectedSort = 'name'.obs;
  bool _isManualRefresh = false;

  @override
  void initState() {
    super.initState();
    _loadUsers();
    _setupSearch();
  }

  /// تحميل المستخدمين
  Future<void> _loadUsers() async {
    _isLoading.value = true;
    try {
      await _adminController.loadUsers();
      _filterUsers();

      // إظهار رسالة نجاح فقط عند التحديث اليدوي
      if (_isManualRefresh) {
        AdminMessageDialog.showSuccess(
          title: 'تم التحديث',
          message: 'تم تحميل ${_adminController.users.length} مستخدم بنجاح',
        );
        _isManualRefresh = false;
      }
    } catch (e) {
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل المستخدمين: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// إعداد البحث
  void _setupSearch() {
    _searchController.addListener(_filterUsers);
    _filteredUsers.assignAll(_adminController.users);
  }

  /// تصفية المستخدمين
  void _filterUsers() {
    var users = _adminController.users.toList();

    // تطبيق البحث النصي
    final query = _searchController.text.toLowerCase();
    if (query.isNotEmpty) {
      users = users
          .where((user) =>
              user.name.toLowerCase().contains(query) ||
              (user.email?.toLowerCase().contains(query) ?? false) ||
              (user.username?.toLowerCase().contains(query) ?? false))
          .toList();
    }

    // تطبيق الفلتر
    switch (_selectedFilter.value) {
      case 'active':
        users = users.where((user) => user.isActive).toList();
        break;
      case 'inactive':
        users = users.where((user) => !user.isActive).toList();
        break;
      case 'recent':
        users = users.where((user) {
          final createdDate =
              DateTime.fromMillisecondsSinceEpoch(user.createdAt * 1000);
          final daysDiff = DateTime.now().difference(createdDate).inDays;
          return daysDiff <= 7; // المستخدمين الجدد خلال أسبوع
        }).toList();
        break;
      default:
        // فلترة حسب معرف الدور (ديناميكي)
        if (_selectedFilter.value.startsWith('role_')) {
          final roleId = int.tryParse(_selectedFilter.value.substring(5));
          if (roleId != null) {
            users = users.where((user) => user.roleId == roleId).toList();
          }
        }
        break;
    }

    // تطبيق الترتيب
    switch (_selectedSort.value) {
      case 'name':
        users.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'email':
        users.sort((a, b) => (a.email ?? '').compareTo(b.email ?? ''));
        break;
      case 'created':
        users
            .sort((a, b) => b.createdAt.compareTo(a.createdAt)); // الأحدث أولاً
        break;
      case 'status':
        users.sort(
            (a, b) => b.isActive.toString().compareTo(a.isActive.toString()));
        break;
    }

    _filteredUsers.assignAll(users);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المستخدمين'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // زر إضافة مستخدم جديد
          if (_permissionService.canCreateUser())
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showAddUserDialog,
              tooltip: 'إضافة مستخدم جديد',
            ),
          // زر تحديث
          if (_permissionService.canViewAllUsers())
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                _isManualRefresh = true;
                _loadUsers();
              },
              tooltip: 'تحديث',
            ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),

          // الإحصائيات والفلاتر
          _buildStatsAndFilters(),

          // قائمة المستخدمين
          Expanded(
            child: Obx(() {
              if (_isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              if (_filteredUsers.isEmpty) {
                return _buildEmptyState();
              }

              return _buildUsersList();
            }),
          ),
        ],
      ),
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في المستخدمين...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _filterUsers();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  /// بناء قسم الإحصائيات والفلاتر
  Widget _buildStatsAndFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // الإحصائيات
          _buildStatsCards(),

          const SizedBox(height: 12),

          // الفلاتر والترتيب
          _buildFiltersRow(),
        ],
      ),
    );
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatsCards() {
    return Obx(() {
      final allUsers = _adminController.users;
      final activeUsers = allUsers.where((u) => u.isActive).length;
      final inactiveUsers = allUsers.length - activeUsers;
      final recentUsers = allUsers.where((user) {
        final createdDate =
            DateTime.fromMillisecondsSinceEpoch(user.createdAt * 1000);
        return DateTime.now().difference(createdDate).inDays <= 7;
      }).length;

      return Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي المستخدمين',
              '${allUsers.length}',
              Icons.people,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'المستخدمين النشطين',
              '$activeUsers',
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'غير النشطين',
              '$inactiveUsers',
              Icons.cancel,
              Colors.red,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'جدد هذا الأسبوع',
              '$recentUsers',
              Icons.new_releases,
              Colors.orange,
            ),
          ),
        ],
      );
    });
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            Text(
              title,
              style: TextStyle(fontSize: 12,color: AppColors.textSecondary),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              
              softWrap: true,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صف الفلاتر
  Widget _buildFiltersRow() {
    return Row(
      children: [
        // فلتر الحالة
        Expanded(
          child: Obx(() => DropdownButtonFormField<String>(
                value: _selectedFilter.value,
                decoration: const InputDecoration(
                  labelText: 'تصفية حسب',
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: _buildFilterItems(),
                onChanged: (value) {
                  if (value != null) {
                    _selectedFilter.value = value;
                    _filterUsers();
                  }
                },
              )),
        ),

        const SizedBox(width: 12),

        // ترتيب النتائج
        Expanded(
          child: Obx(() => DropdownButtonFormField<String>(
                value: _selectedSort.value,
                decoration: const InputDecoration(
                  labelText: 'ترتيب حسب',
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: const [
                  DropdownMenuItem(value: 'name', child: Text('الاسم')),
                  DropdownMenuItem(
                      value: 'email', child: Text('البريد الإلكتروني')),
                  DropdownMenuItem(
                      value: 'created', child: Text('تاريخ الإنشاء')),
                  DropdownMenuItem(value: 'status', child: Text('الحالة')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    _selectedSort.value = value;
                    _filterUsers();
                  }
                },
              )),
        ),
      ],
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchController.text.isNotEmpty
                ? 'لا توجد نتائج للبحث'
                : 'لا يوجد مستخدمين',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchController.text.isNotEmpty
                ? 'جرب كلمات بحث مختلفة'
                : 'ابدأ بإضافة مستخدم جديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
          if (_permissionService.canCreateUser()) ...[
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _showAddUserDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة مستخدم جديد'),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء قائمة المستخدمين
  Widget _buildUsersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredUsers.length,
      itemBuilder: (context, index) {
        final user = _filteredUsers[index];
        return _buildUserCard(user);
      },
    );
  }

  /// بناء بطاقة المستخدم
  Widget _buildUserCard(User user) {
    final createdDate =
        DateTime.fromMillisecondsSinceEpoch(user.createdAt * 1000);
    final daysSinceCreated = DateTime.now().difference(createdDate).inDays;
    final isNewUser = daysSinceCreated <= 7;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                // صورة المستخدم
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundColor:
                          user.isActive ? Colors.green : Colors.grey,
                      backgroundImage: user.profileImage != null &&
                              user.profileImage!.isNotEmpty
                          ? NetworkImage(user.profileImage!)
                          : null,
                      child: user.profileImage == null ||
                              user.profileImage!.isEmpty
                          ? Text(
                              user.name.substring(0, 1).toUpperCase(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 20,
                              ),
                            )
                          : null,
                    ),
                    if (isNewUser)
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(
                            color: Colors.orange,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.new_releases,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                      ),
                  ],
                ),

                const SizedBox(width: 16),

                // معلومات المستخدم
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              user.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                          _buildStatusChip(user.isActive),
                        ],
                      ),

                      const SizedBox(height: 4),

                      Row(
                        children: [
                          const Icon(Icons.email, size: 14, color: Colors.grey),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              user.email ?? 'غير محدد',
                              style: const TextStyle(color: Colors.grey),
                            ),
                          ),
                        ],
                      ),

                      if (user.username != null) ...[
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            const Icon(Icons.person,
                                size: 14, color: Colors.grey),
                            const SizedBox(width: 4),
                            Text(
                              user.username!,
                              style: const TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      ],

                      const SizedBox(height: 4),

                      // عرض الدور
                      if (user.role != null) ...[
                        Row(
                          children: [
                            const Icon(Icons.admin_panel_settings,
                                size: 14, color: Colors.blue),
                            const SizedBox(width: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: _getRoleColor(user.roleId ?? 0)
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: _getRoleColor(user.roleId ?? 0),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                user.role!.displayName,
                                style: TextStyle(
                                  color: _getRoleColor(user.roleId ?? 0),
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                      ],

                      Row(
                        children: [
                          const Icon(Icons.calendar_today,
                              size: 14, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            'انضم منذ ${_formatDaysAgo(daysSinceCreated)}',
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // قائمة الإجراءات
                PopupMenuButton<String>(
                  onSelected: (value) => _handleUserAction(value, user),
                  icon: const Icon(Icons.more_vert),
                  itemBuilder: (context) => [
                    if (_permissionService.canEditUser())
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit, color: Colors.blue),
                          title: Text('تعديل'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'permissions',
                      child: ListTile(
                        leading: Icon(Icons.security, color: Colors.purple),
                        title: Text('الصلاحيات'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'copy_permissions',
                      child: ListTile(
                        leading: Icon(Icons.copy, color: Colors.teal),
                        title: Text('نسخ الصلاحيات'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    PopupMenuItem(
                      value: user.isActive ? 'deactivate' : 'activate',
                      child: ListTile(
                        leading: Icon(
                          user.isActive ? Icons.block : Icons.check_circle,
                          color: user.isActive ? Colors.red : Colors.green,
                        ),
                        title: Text(user.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    if (_permissionService.canDeleteUser())
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete, color: Colors.red),
                          title:
                              Text('حذف', style: TextStyle(color: Colors.red)),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شارة الحالة
  Widget _buildStatusChip(bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isActive
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isActive ? Icons.check_circle : Icons.cancel,
            size: 12,
            color: isActive ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 4),
          Text(
            isActive ? 'نشط' : 'غير نشط',
            style: TextStyle(
              color: isActive ? Colors.green : Colors.red,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق عدد الأيام
  String _formatDaysAgo(int days) {
    if (days == 0) return 'اليوم';
    if (days == 1) return 'أمس';
    if (days < 7) return '$days أيام';
    if (days < 30) return '${(days / 7).floor()} أسابيع';
    if (days < 365) return '${(days / 30).floor()} شهور';
    return '${(days / 365).floor()} سنوات';
  }

  /// معالجة إجراءات المستخدم
  void _handleUserAction(String action, User user) {
    switch (action) {
      case 'edit':
        _showEditUserDialog(user);
        break;
      case 'permissions':
        _showUserPermissions(user);
        break;
      case 'activate':
      case 'deactivate':
        _toggleUserStatus(user);
        break;
      case 'delete':
        _deleteUser(user);
        break;
    }
  }

  /// عرض حوار إضافة مستخدم
  void _showAddUserDialog() {
    Get.dialog(
      const UserFormDialog(),
    ).then((result) {
      if (result == true) {
        _loadUsers();
      }
    });
  }

  /// عرض حوار تعديل مستخدم
  void _showEditUserDialog(User user) {
    Get.dialog(
      UserFormDialog(user: user),
    ).then((result) {
      if (result == true) {
        _loadUsers();
      }
    });
  }

  /// تبديل حالة المستخدم
  Future<void> _toggleUserStatus(User user) async {
    final action = user.isActive ? 'إلغاء تفعيل' : 'تفعيل';
    final confirmed = await AdminConfirmDialog.show(
      title: '$action المستخدم',
      message: 'هل أنت متأكد من $action المستخدم "${user.name}"؟',
      confirmText: action,
      icon: user.isActive ? Icons.block : Icons.check_circle,
      confirmColor: user.isActive ? Colors.red : Colors.green,
    );

    if (confirmed) {
      try {
        AdminLoadingDialog.show(message: 'جاري $action المستخدم...');

        // استدعاء API لتبديل حالة المستخدم
        final updatedUser = user.copyWith(isActive: !user.isActive);
        await _adminController.updateUser(updatedUser);

        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم $action المستخدم بنجاح',
        );

        _loadUsers();
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في $action المستخدم: $e',
        );
      }
    }
  }

  /// حذف المستخدم
  Future<void> _deleteUser(User user) async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'حذف المستخدم',
      message:
          'هل أنت متأكد من حذف المستخدم "${user.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.',
      confirmText: 'حذف',
      icon: Icons.delete,
      confirmColor: Colors.red,
    );

    if (confirmed) {
      try {
        AdminLoadingDialog.show(message: 'جاري حذف المستخدم...');

        // استدعاء API لحذف المستخدم
        await _adminController.deleteUser(user.id);

        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم حذف المستخدم بنجاح',
        );

        _loadUsers();
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في حذف المستخدم: $e',
        );
      }
    }
  }

  /// عرض صلاحيات المستخدم
  void _showUserPermissions(User user) {
    Get.dialog(
      UserPermissionsDialog(user: user),
    ).then((result) {
      if (result == true) {
        // تم حفظ الصلاحيات بنجاح - إعادة تحميل البيانات
        _loadUsers();
      }
    });
  }

  /// بناء قائمة الفلاتر الديناميكية
  List<DropdownMenuItem<String>> _buildFilterItems() {
    List<DropdownMenuItem<String>> items = [
      const DropdownMenuItem(value: 'all', child: Text('جميع المستخدمين')),
      const DropdownMenuItem(value: 'active', child: Text('النشطين فقط')),
      const DropdownMenuItem(value: 'inactive', child: Text('غير النشطين')),
      const DropdownMenuItem(value: 'recent', child: Text('الجدد (أسبوع)')),
    ];

    // إضافة الأدوار الديناميكية
    final uniqueRoles = <int, String>{};
    for (var user in _adminController.users) {
      if (user.role != null && user.roleId != null) {
        uniqueRoles[user.roleId!] = user.role!.displayName;
      }
    }

    for (var entry in uniqueRoles.entries) {
      items.add(
        DropdownMenuItem(
          value: 'role_${entry.key}',
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getRoleColor(entry.key),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(entry.value),
            ],
          ),
        ),
      );
    }

    return items;
  }

  /// تحديد لون الدور حسب معرفه (ديناميكي)
  Color _getRoleColor(int roleId) {
    // ألوان متدرجة حسب معرف الدور
    final colors = [
      Colors.red,
      Colors.orange,
      Colors.purple,
      Colors.blue,
      Colors.green,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];
    return colors[roleId % colors.length];
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
