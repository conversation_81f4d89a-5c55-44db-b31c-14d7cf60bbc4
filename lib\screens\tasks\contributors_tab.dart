import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/task_controller.dart';
import '../../models/task_contributor_model.dart';

/// تبويب المساهمين - بسيط وخالي من التعقيد
class ContributorsTab extends StatelessWidget {
  final String taskId;

  const ContributorsTab({
    super.key,
    required this.taskId,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TaskController>(
      id: 'task_contributors', // إضافة معرف للتحديث المحدد
      builder: (controller) {
        final contributors = controller.taskContributors;

        if (contributors.isEmpty) {
          return _buildEmptyState();
        }

        return Column(
          children: [
            // ✅ جعل الهيدر مرن ليتكيف مع المساحة المتاحة
            Flexible(
              flex: 0,
              child: _buildHeader(contributors),
            ),
            // ✅ جعل التعليمات مرنة أيضاً
            Flexible(
              flex: 0,
              child: _buildInstructions(),
            ),
            // ✅ إعطاء باقي المساحة للقائمة
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: contributors.length,
                itemBuilder: (context, index) {
                  return _buildContributorCard(contributors[index]);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.textSecondary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.people_outline,
              size: 64,
              color: AppColors.textSecondary.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد مساهمون في هذه المهمة',
            style: AppStyles.titleMedium.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر هنا قائمة بجميع المساهمين عند إضافتهم',
            style: AppStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// تعليمات الاستخدام
  Widget _buildInstructions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 4), // ✅ تقليل margin أكثر
      padding: const EdgeInsets.all(8), // ✅ تقليل padding أكثر
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8), // ✅ تقليل border radius
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.info.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min, // ✅ تقليل المساحة المستخدمة
        children: [
          Icon(Icons.info_outline, color: AppColors.info, size: 16), // ✅ تقليل حجم الأيقونة
          const SizedBox(width: 8), // ✅ تقليل المساحة
          Expanded(
            child: Text(
              'انقر على أي مساهم لعرض تفاصيل مساهماته',
              style: AppStyles.bodySmall.copyWith( // ✅ تقليل حجم النص
                color: AppColors.info,
                fontWeight: FontWeight.w600,
                fontSize: 12, // ✅ تقليل حجم الخط
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// رأس التبويب مع الإحصائيات
  Widget _buildHeader(List<TaskContributor> contributors) {
    final activeCount = contributors.where((c) => c.isActiveContributor).length;
    final totalContribution = contributors.fold<double>(
      0.0,
      (sum, c) => sum + c.contributionPercentage,
    );

    return GetBuilder<TaskController>(
      id: 'task_contributors',
      builder: (controller) {
        final task = controller.currentTask;
        final commentsCount = task?.comments.length ?? 0;
        final attachmentsCount = task?.attachments.length ?? 0;

        return Container(
          padding: const EdgeInsets.all(8), // ✅ تقليل padding أكثر
          decoration: BoxDecoration(
            color: AppColors.surface.withValues(alpha: 0.8),
            border: Border(
              bottom: BorderSide(
                color: AppColors.border.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min, // ✅ تقليل المساحة المستخدمة
            children: [
              // الصف الأول - إحصائيات المساهمين
              Row(
                children: [
                  _buildStatCard('إجمالي المساهمين', '${contributors.length}', Icons.people),
                  const SizedBox(width: 8), // ✅ تقليل المساحة أكثر
                  _buildStatCard('المساهمون النشطون', '$activeCount', Icons.person_add),
                  const SizedBox(width: 8), // ✅ تقليل المساحة أكثر
                  _buildStatCard('إجمالي المساهمة', '${totalContribution.toStringAsFixed(1)}%', Icons.pie_chart),
                ],
              ),
              const SizedBox(height: 8), // ✅ تقليل المساحة أكثر
              // الصف الثاني - إحصائيات المهمة
              Row(
                children: [
                  _buildStatCard('التعليقات', '$commentsCount', Icons.comment),
                  const SizedBox(width: 8), // ✅ تقليل المساحة أكثر
                  _buildStatCard('المرفقات', '$attachmentsCount', Icons.attach_file),
                  const SizedBox(width: 8), // ✅ تقليل المساحة أكثر
                  _buildStatCard('التقدم', '${task?.completionPercentage ?? 0}%', Icons.trending_up),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  /// بطاقة إحصائية محسنة للتباين العالي
  Widget _buildStatCard(String title, String value, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8), // ✅ تقليل padding أكثر
        decoration: BoxDecoration(
          color: AppColors.card,
          borderRadius: BorderRadius.circular(8), // ✅ تقليل border radius
          border: Border.all(
            color: AppColors.border.withValues(alpha: 0.4),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة مع خلفية دائرية مصغرة
            Container(
              padding: const EdgeInsets.all(6), // ✅ تقليل padding الأيقونة
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Icon(
                icon,
                color: AppColors.primary,
                size: 16, // ✅ تقليل حجم الأيقونة
              ),
            ),
            const SizedBox(height: 8), // ✅ تقليل المساحة
            // القيمة
            Text(
              value,
              style: AppStyles.titleMedium.copyWith( // ✅ تقليل حجم النص
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
                fontSize: 16, // ✅ تقليل حجم الخط
              ),
            ),
            const SizedBox(height: 4), // ✅ تقليل المساحة
            // العنوان
            Text(
              title,
              style: AppStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w600,
                fontSize: 10, // ✅ تقليل حجم الخط
              ),
              textAlign: TextAlign.center,
              maxLines: 1, // ✅ سطر واحد فقط
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة المساهم
  Widget _buildContributorCard(TaskContributor contributor) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _viewContributions(contributor),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _getContributorColor(contributor).withValues(alpha: 0.4),
              width: 2,
            ),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                _getContributorColor(contributor).withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // صورة المساهم
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: _getContributorColor(contributor).withValues(alpha: 0.3),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: 24,
                    backgroundColor: _getContributorColor(contributor),
                    backgroundImage: contributor.userProfileImage?.isNotEmpty == true
                        ? NetworkImage(contributor.userProfileImage!)
                        : null,
                    child: contributor.userProfileImage?.isNotEmpty != true
                        ? Text(
                            contributor.userName.isNotEmpty ? contributor.userName[0].toUpperCase() : '?',
                            style: AppStyles.titleMedium.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                ),
                const SizedBox(width: 16),

                // معلومات المساهم
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        contributor.userName,
                        style: AppStyles.titleMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        contributor.userEmail,
                        style: AppStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: contributor.isActiveContributor
                              ? AppColors.success.withValues(alpha: 0.1)
                              : AppColors.textSecondary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: contributor.isActiveContributor
                                ? AppColors.success.withValues(alpha: 0.3)
                                : AppColors.textSecondary.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              contributor.isActiveContributor ? Icons.check_circle : Icons.circle_outlined,
                              size: 16,
                              color: contributor.isActiveContributor ? AppColors.success : AppColors.textSecondary,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              contributor.isActiveContributor ? 'مساهم نشط' : 'مساهم محتمل',
                              style: AppStyles.labelSmall.copyWith(
                                color: contributor.isActiveContributor ? AppColors.success : AppColors.textSecondary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // إحصائيات المساهم
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        // خلفية قوية للتباين العالي في جميع الحالات
                        color: contributor.isActiveContributor
                            ? _getContributorColor(contributor).withValues(alpha: 0.15)
                            : AppColors.textSecondary.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          // حدود قوية للتباين العالي
                          color: contributor.isActiveContributor
                              ? _getContributorColor(contributor).withValues(alpha: 0.5)
                              : AppColors.textSecondary.withValues(alpha: 0.8),
                          width: 2,
                        ),
                        // ظل قوي للتباين
                        boxShadow: [
                          BoxShadow(
                            color: contributor.isActiveContributor
                                ? _getContributorColor(contributor).withValues(alpha: 0.3)
                                : AppColors.textSecondary.withValues(alpha: 0.4),
                            blurRadius: 6,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // عرض النسبة أو حالة "لا توجد مساهمة" للقيم الصفر
                          contributor.contributionPercentage <= 0
                              ? Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: AppColors.textSecondary.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: AppColors.textSecondary.withValues(alpha: 0.4),
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    'لا توجد مساهمة',
                                    style: AppStyles.labelSmall.copyWith(
                                      color: AppColors.textSecondary,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 10,
                                    ),
                                  ),
                                )
                              : Text(
                                  '${contributor.contributionPercentage.toStringAsFixed(1)}%',
                                  style: AppStyles.titleMedium.copyWith(
                                    color: _getContributorColor(contributor),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                          const SizedBox(height: 6),
                          Text(
                            '${contributor.totalUpdates} تحديث',
                            style: AppStyles.bodySmall.copyWith(
                              // لون قوي ومتباين للنص
                              color: AppColors.textPrimary,
                              fontWeight: FontWeight.w700,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${contributor.totalComments} تعليق',
                            style: AppStyles.bodySmall.copyWith(
                              // لون قوي ومتباين للنص
                              color: AppColors.textPrimary,
                              fontWeight: FontWeight.w700,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // سهم للإشارة إلى إمكانية النقر
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// لون المساهم حسب نسبة المساهمة
  Color _getContributorColor(TaskContributor contributor) {
    if (!contributor.isActiveContributor) return AppColors.textSecondary;
    if (contributor.contributionPercentage >= 50) return AppColors.success;
    if (contributor.contributionPercentage >= 25) return AppColors.warning;
    if (contributor.contributionPercentage > 0) return AppColors.primary;
    return AppColors.textSecondary;
  }

  /// عرض مساهمات المساهم - انتقال مباشر
  void _viewContributions(TaskContributor contributor) {
    debugPrint('تم النقر على المساهم: ${contributor.userName}');

    // تحديث المساهم المحدد في الكونترولر
    final taskController = Get.find<TaskController>();
    taskController.selectedContributorId = contributor.userId.toString();
    debugPrint('تم تحديد المساهم: ${taskController.selectedContributorId}');

    // الانتقال المباشر إلى تبويب تفاصيل المساهمات
    try {
      final tabController = Get.find<TabController>(tag: 'task_detail_tabs');
      debugPrint('تم العثور على TabController، الانتقال إلى التبويب 11');
      tabController.animateTo(11); // تبويب تفاصيل المساهمات
    } catch (e) {
      debugPrint('خطأ في الانتقال إلى التبويب: $e');
      // محاولة بديلة
      Get.snackbar(
        'تم اختيار المساهم',
        'انتقل يدوياً إلى تبويب تفاصيل المساهمات لعرض مساهمات ${contributor.userName}',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}