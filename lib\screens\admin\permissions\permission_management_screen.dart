import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../models/permission_models.dart';
import '../../../services/unified_permission_service.dart';
import '../../../services/admin/unified_admin_api_service.dart';
import '../../../constants/app_colors.dart';
import '../shared/admin_dialog_widget.dart';
import '../shared/admin_card_widget.dart';

/// شاشة إدارة الصلاحيات
class PermissionManagementScreen extends StatefulWidget {
  const PermissionManagementScreen({super.key});

  @override
  State<PermissionManagementScreen> createState() => _PermissionManagementScreenState();
}

class _PermissionManagementScreenState extends State<PermissionManagementScreen> {
  final AdminController _adminController = Get.find<AdminController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final TextEditingController _searchController = TextEditingController();
  final RxList<Permission> _filteredPermissions = <Permission>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _selectedGroup = 'الكل'.obs;

  @override
  void initState() {
    super.initState();
    _loadPermissions();
    _setupSearch();
  }

  /// تحميل الصلاحيات
  Future<void> _loadPermissions() async {
    _isLoading.value = true;
    try {
      await _adminController.loadPermissions();
      _filterPermissions();
    } catch (e) {
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل الصلاحيات: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// إعداد البحث
  void _setupSearch() {
    _searchController.addListener(_filterPermissions);
    _filteredPermissions.assignAll(_adminController.permissions);
  }

  /// تصفية الصلاحيات
  void _filterPermissions() {
    final query = _searchController.text.toLowerCase();
    final selectedGroup = _selectedGroup.value;
    
    var filtered = _adminController.permissions.where((permission) {
      final matchesSearch = query.isEmpty ||
          permission.name.toLowerCase().contains(query) ||
          (permission.description?.toLowerCase().contains(query) ?? false);
      
      final matchesGroup = selectedGroup == 'الكل' ||
          permission.permissionGroup == selectedGroup;
      
      return matchesSearch && matchesGroup;
    }).toList();
    
    _filteredPermissions.assignAll(filtered);
  }

  /// الحصول على مجموعات الصلاحيات
  List<String> get _permissionGroups {
    final groups = _adminController.permissions
        .map((p) => p.permissionGroup)
        .toSet()
        .toList();
    groups.sort();
    return ['الكل', ...groups];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.security,
                color: AppColors.appBarIcon,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'إدارة الصلاحيات',
              style: TextStyle(
                color: AppColors.appBarText,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.appBar,
        foregroundColor: AppColors.appBarText,
        elevation: 2,
        shadowColor: AppColors.getShadowColor(0.1),
        actions: [
          if (_permissionService.canManagePermissions()) ...[
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.success.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: IconButton(
                icon: Icon(Icons.add, color: AppColors.success),
                onPressed: _showAddPermissionDialog,
                tooltip: 'إضافة صلاحية جديدة',
              ),
            ),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.info.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: IconButton(
                icon: Icon(Icons.refresh, color: AppColors.info),
                onPressed: _loadPermissions,
                tooltip: 'تحديث',
              ),
            ),
            const SizedBox(width: 8),
          ],
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والتصفية
          _buildSearchAndFilter(),
          
          // قائمة الصلاحيات
          Expanded(
            child: Obx(() {
              if (_isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              
              if (_filteredPermissions.isEmpty) {
                return _buildEmptyState();
              }
              
              return _buildPermissionsList();
            }),
          ),
        ],
      ),
    );
  }

  /// بناء شريط البحث والتصفية
  Widget _buildSearchAndFilter() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.getBorderColor(),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.getShadowColor(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.search,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'البحث والتصفية',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // شريط البحث
          TextField(
            controller: _searchController,
            style: TextStyle(color: AppColors.textPrimary),
            decoration: InputDecoration(
              hintText: 'البحث في الصلاحيات...',
              hintStyle: TextStyle(color: AppColors.textHint),
              prefixIcon: Icon(Icons.search, color: AppColors.primary),
              suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear, color: AppColors.textSecondary),
                    onPressed: () {
                      _searchController.clear();
                      _filterPermissions();
                    },
                  )
                : null,
              filled: true,
              fillColor: AppColors.getContainerColor(lighter: true),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.getBorderColor()),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.getBorderColor()),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.primary, width: 2),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // تصفية المجموعات
          Row(
            children: [
              Icon(Icons.filter_list, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'المجموعة:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: AppColors.getContainerColor(lighter: true),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.getBorderColor()),
                  ),
                  child: Obx(() => DropdownButton<String>(
                    value: _selectedGroup.value,
                    isExpanded: true,
                    underline: const SizedBox(),
                    style: TextStyle(color: AppColors.textPrimary),
                    dropdownColor: AppColors.surface,
                    items: _permissionGroups.map((group) {
                      return DropdownMenuItem<String>(
                        value: group,
                        child: Text(
                          group,
                          style: TextStyle(color: AppColors.textPrimary),
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        _selectedGroup.value = value;
                        _filterPermissions();
                      }
                    },
                  )),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    final isSearching = _searchController.text.isNotEmpty || _selectedGroup.value != 'الكل';

    return Center(
      child: Container(
        margin: const EdgeInsets.all(32),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppColors.getBorderColor(),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.getShadowColor(0.05),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isSearching
                    ? AppColors.warning.withValues(alpha: 0.1)
                    : AppColors.textSecondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                isSearching ? Icons.search_off : Icons.security_outlined,
                size: 48,
                color: isSearching ? AppColors.warning : AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              isSearching ? 'لا توجد نتائج للبحث' : 'لا يوجد صلاحيات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isSearching
                  ? 'جرب كلمات بحث مختلفة أو غير المجموعة'
                  : 'لا توجد صلاحيات في النظام',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
            if (isSearching) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () {
                  _searchController.clear();
                  _selectedGroup.value = 'الكل';
                  _filterPermissions();
                },
                icon: const Icon(Icons.clear_all),
                label: const Text('مسح الفلاتر'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء قائمة الصلاحيات
  Widget _buildPermissionsList() {
    // تجميع الصلاحيات حسب المجموعة
    final groupedPermissions = <String, List<Permission>>{};
    for (final permission in _filteredPermissions) {
      groupedPermissions.putIfAbsent(permission.permissionGroup, () => []);
      groupedPermissions[permission.permissionGroup]!.add(permission);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groupedPermissions.length,
      itemBuilder: (context, index) {
        final group = groupedPermissions.keys.elementAt(index);
        final permissions = groupedPermissions[group]!;
        
        return _buildPermissionGroup(group, permissions);
      },
    );
  }

  /// بناء مجموعة الصلاحيات
  Widget _buildPermissionGroup(String group, List<Permission> permissions) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.getBorderColor(),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.getShadowColor(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس المجموعة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              border: Border(
                bottom: BorderSide(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getGroupIcon(group),
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    group,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    '${permissions.length}',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // قائمة الصلاحيات في المجموعة
          Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              children: permissions.map((permission) => _buildPermissionCard(permission)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الصلاحية
  Widget _buildPermissionCard(Permission permission) {
    return AdminCardWidget(
      title: permission.name,
      subtitle: permission.description ?? 'لا يوجد وصف',
      icon: _getPermissionIcon(permission),
      iconColor: _getPermissionColor(permission),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مستوى الصلاحية
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getLevelColor(permission.level).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getLevelColor(permission.level).withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              'مستوى ${permission.level}',
              style: TextStyle(
                color: _getLevelColor(permission.level),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          const SizedBox(width: 8),

          // صلاحية افتراضية
          if (permission.isDefault)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.info.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                'افتراضية',
                style: TextStyle(
                  color: AppColors.info,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

          const SizedBox(width: 8),

          // أزرار الإجراءات
          if (_permissionService.canManagePermissions())
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, size: 20),
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showEditPermissionDialog(permission);
                    break;
                  case 'delete':
                    _showDeletePermissionDialog(permission);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 16),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                if (!permission.isDefault) // لا يمكن حذف الصلاحيات الافتراضية
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
              ],
            ),
        ],
      ),
      onTap: () => _showPermissionDetails(permission),
    );
  }

  /// الحصول على أيقونة المجموعة
  IconData _getGroupIcon(String group) {
    switch (group.toLowerCase()) {
      case 'tasks':
      case 'المهام':
        return Icons.task;
      case 'users':
      case 'المستخدمين':
        return Icons.people;
      case 'admin':
      case 'الإدارة':
        return Icons.admin_panel_settings;
      case 'reports':
      case 'التقارير':
        return Icons.analytics;
      case 'system':
      case 'النظام':
        return Icons.settings;
      default:
        return Icons.security;
    }
  }

  /// الحصول على أيقونة الصلاحية
  IconData _getPermissionIcon(Permission permission) {
    if (permission.name.contains('view')) return Icons.visibility;
    if (permission.name.contains('create')) return Icons.add;
    if (permission.name.contains('edit')) return Icons.edit;
    if (permission.name.contains('delete')) return Icons.delete;
    if (permission.name.contains('manage')) return Icons.settings;
    return Icons.vpn_key;
  }

  /// الحصول على لون الصلاحية
  Color _getPermissionColor(Permission permission) {
    if (permission.name.contains('view')) return AppColors.info;
    if (permission.name.contains('create')) return AppColors.success;
    if (permission.name.contains('edit')) return AppColors.warning;
    if (permission.name.contains('delete')) return AppColors.error;
    if (permission.name.contains('manage')) return AppColors.primary;
    return AppColors.textSecondary;
  }

  /// الحصول على لون المستوى
  Color _getLevelColor(int level) {
    switch (level) {
      case 1:
        return AppColors.success;
      case 2:
        return AppColors.info;
      case 3:
        return AppColors.warning;
      case 4:
        return AppColors.error;
      case 5:
        return AppColors.primary;
      default:
        return AppColors.textSecondary;
    }
  }

  /// إضافة صلاحية جديدة
  void _showAddPermissionDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    final groupController = TextEditingController();
    final categoryController = TextEditingController();
    final levelController = TextEditingController(text: '1');
    bool isDefault = false;

    Get.dialog(
      Dialog(
        backgroundColor: AppColors.surface,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: AppColors.getBorderColor(),
            width: 1,
          ),
        ),
        child: Container(
          width: 500,
          constraints: const BoxConstraints(maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  border: Border(
                    bottom: BorderSide(
                      color: AppColors.success.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.add_circle,
                        color: AppColors.success,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'إضافة صلاحية جديدة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الصلاحية *',
                    hintText: 'مثال: canViewTasks',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف',
                    hintText: 'وصف مختصر للصلاحية',
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: groupController,
                  decoration: const InputDecoration(
                    labelText: 'المجموعة *',
                    hintText: 'مثال: المهام، المستخدمين، الإدارة',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: categoryController,
                  decoration: const InputDecoration(
                    labelText: 'الفئة',
                    hintText: 'فئة فرعية للصلاحية',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: levelController,
                  decoration: const InputDecoration(
                    labelText: 'المستوى (1-5) *',
                    hintText: '1 = أساسي، 5 = متقدم',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                StatefulBuilder(
                  builder: (context, setState) {
                    return CheckboxListTile(
                      title: const Text('صلاحية افتراضية'),
                      subtitle: const Text('تُمنح تلقائياً للمستخدمين الجدد'),
                      value: isDefault,
                      onChanged: (value) {
                        setState(() {
                          isDefault = value ?? false;
                        });
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ),

        // Actions
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.getContainerColor(lighter: true),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(16),
              bottomRight: Radius.circular(16),
            ),
            border: Border(
              top: BorderSide(
                color: AppColors.getBorderColor(),
                width: 1,
              ),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              OutlinedButton(
                onPressed: () => Get.back(),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  side: BorderSide(color: AppColors.getBorderColor()),
                ),
                child: Text(
                  'إلغاء',
                  style: TextStyle(color: AppColors.textPrimary),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: () => _createPermission(
                  nameController.text,
                  descriptionController.text,
                  groupController.text,
                  categoryController.text,
                  int.tryParse(levelController.text) ?? 1,
                  isDefault,
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  foregroundColor: AppColors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  elevation: 2,
                ),
                child: const Text(
                  'إضافة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  ),
  ),
    );
  }

  /// إنشاء صلاحية جديدة
  Future<void> _createPermission(
    String name,
    String description,
    String group,
    String category,
    int level,
    bool isDefault,
  ) async {
    if (name.trim().isEmpty || group.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يجب ملء الحقول المطلوبة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      // التحقق من عدم وجود صلاحية بنفس الاسم
      final existingPermission = _filteredPermissions.firstWhereOrNull(
        (p) => p.name.toLowerCase() == name.toLowerCase(),
      );

      if (existingPermission != null) {
        Get.snackbar(
          'خطأ',
          'يوجد صلاحية بنفس الاسم بالفعل',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // إضافة الصلاحية عبر API
      await Get.find<UnifiedAdminApiService>().createPermission(
        name: name.trim(),
        description: description.trim().isEmpty ? null : description.trim(),
        screenId: null, // يمكن إضافة اختيار الشاشة لاحقاً
      );

      Get.back(); // إغلاق الحوار

      // تحديث القائمة
      await _loadPermissions();

      Get.snackbar(
        'نجح',
        'تم إضافة الصلاحية بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في إضافة الصلاحية: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// تعديل صلاحية موجودة
  void _showEditPermissionDialog(Permission permission) {
    final nameController = TextEditingController(text: permission.name);
    final descriptionController = TextEditingController(text: permission.description ?? '');
    final groupController = TextEditingController(text: permission.permissionGroup);
    final categoryController = TextEditingController(text: permission.category ?? '');
    final levelController = TextEditingController(text: permission.level.toString());
    bool isDefault = permission.isDefault;

    Get.dialog(
      AlertDialog(
        title: const Text('تعديل الصلاحية'),
        content: SizedBox(
          width: 400,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الصلاحية *',
                    hintText: 'مثال: canViewTasks',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف',
                    hintText: 'وصف مختصر للصلاحية',
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: groupController,
                  decoration: const InputDecoration(
                    labelText: 'المجموعة *',
                    hintText: 'مثال: المهام، المستخدمين، الإدارة',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: categoryController,
                  decoration: const InputDecoration(
                    labelText: 'الفئة',
                    hintText: 'فئة فرعية للصلاحية',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: levelController,
                  decoration: const InputDecoration(
                    labelText: 'المستوى (1-5) *',
                    hintText: '1 = أساسي، 5 = متقدم',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                StatefulBuilder(
                  builder: (context, setState) {
                    return CheckboxListTile(
                      title: const Text('صلاحية افتراضية'),
                      subtitle: const Text('تُمنح تلقائياً للمستخدمين الجدد'),
                      value: isDefault,
                      onChanged: (value) {
                        setState(() {
                          isDefault = value ?? false;
                        });
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _updatePermission(
              permission.id,
              nameController.text,
              descriptionController.text,
              groupController.text,
              categoryController.text,
              int.tryParse(levelController.text) ?? 1,
              isDefault,
            ),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// تحديث صلاحية موجودة
  Future<void> _updatePermission(
    int id,
    String name,
    String description,
    String group,
    String category,
    int level,
    bool isDefault,
  ) async {
    if (name.trim().isEmpty || group.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يجب ملء الحقول المطلوبة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      // تحديث الصلاحية عبر API
      await Get.find<UnifiedAdminApiService>().updatePermission(
        id: id,
        name: name.trim(),
        description: description.trim().isEmpty ? null : description.trim(),
        screenId: null, // يمكن إضافة اختيار الشاشة لاحقاً
      );

      Get.back(); // إغلاق الحوار

      // تحديث القائمة
      await _loadPermissions();

      Get.snackbar(
        'نجح',
        'تم تحديث الصلاحية بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث الصلاحية: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// حذف صلاحية
  void _showDeletePermissionDialog(Permission permission) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف الصلاحية "${permission.name}"؟'),
            const SizedBox(height: 8),
            const Text(
              'تحذير: سيتم إزالة هذه الصلاحية من جميع المستخدمين والأدوار.',
              style: TextStyle(color: Colors.red, fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _deletePermission(permission.id),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// حذف صلاحية
  Future<void> _deletePermission(int id) async {
    try {
      // حذف الصلاحية عبر API
      await Get.find<UnifiedAdminApiService>().deletePermission(id);

      Get.back(); // إغلاق الحوار

      // تحديث القائمة
      await _loadPermissions();

      Get.snackbar(
        'نجح',
        'تم حذف الصلاحية بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في حذف الصلاحية: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// عرض تفاصيل الصلاحية
  void _showPermissionDetails(Permission permission) {
    Get.dialog(
      AlertDialog(
        title: Text(permission.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('الاسم', permission.name),
            _buildDetailRow('الوصف', permission.description ?? 'لا يوجد وصف'),
            _buildDetailRow('المجموعة', permission.permissionGroup),
            _buildDetailRow('الفئة', permission.category ?? 'غير محدد'),
            _buildDetailRow('المستوى', permission.level.toString()),
            _buildDetailRow('افتراضية', permission.isDefault ? 'نعم' : 'لا'),
            if (permission.screen != null)
              _buildDetailRow('الشاشة', permission.screen!.name),
            if (permission.action != null)
              _buildDetailRow('الإجراء', permission.action!.name),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
