import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:get/get.dart';
import '../../../models/role_model.dart';
import '../../../models/permission_models.dart';
import '../../../controllers/admin_controller.dart';
import '../../../controllers/auth_controller.dart';
import '../../../services/api/roles_api_service.dart';

import '../shared/admin_dialog_widget.dart';

/// حوار إدارة صلاحيات الدور
class RolePermissionsDialog extends StatefulWidget {
  final Role role;
  final VoidCallback? onSuccess;

  const RolePermissionsDialog({
    super.key,
    required this.role,
    this.onSuccess,
  });

  @override
  State<RolePermissionsDialog> createState() => _RolePermissionsDialogState();
}

class _RolePermissionsDialogState extends State<RolePermissionsDialog> {
  final AdminController _adminController = Get.find<AdminController>();
  final AuthController _authController = Get.find<AuthController>();
  final RolesApiService _rolesApiService = RolesApiService();

  // حالة الحوار
  final RxBool _isLoading = false.obs;
  final RxBool _isSaving = false.obs;
  final RxSet<int> _selectedPermissions = <int>{}.obs;
  final RxSet<int> _originalPermissions = <int>{}.obs;
  final RxBool _isExpanded = false.obs;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    _isLoading.value = true;
    try {
      // تحميل جميع الصلاحيات
      await _adminController.loadPermissions();
      
      // تحميل صلاحيات الدور الحالية
      await _loadRolePermissions();
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل البيانات: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل صلاحيات الدور الحالية
  Future<void> _loadRolePermissions() async {
    try {
      debugPrint('🔄 بدء تحميل صلاحيات الدور ${widget.role.id}...');
      final permissions = await _rolesApiService.getRolePermissions(widget.role.id);
      debugPrint('✅ تم جلب ${permissions.length} صلاحية للدور');

      final permissionIds = permissions.map((p) => p.id).toSet();
      debugPrint('📋 معرفات الصلاحيات: $permissionIds');

      _selectedPermissions.assignAll(permissionIds);
      _originalPermissions.assignAll(permissionIds);

      debugPrint('✅ تم تحديد ${_selectedPermissions.length} صلاحية في الواجهة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل صلاحيات الدور: $e');
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل صلاحيات الدور: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      backgroundColor: theme.colorScheme.surface,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Obx(() => Container(
        width: _isExpanded.value
            ? MediaQuery.of(context).size.width * 0.98
            : MediaQuery.of(context).size.width * 0.9,
        height: _isExpanded.value
            ? MediaQuery.of(context).size.height * 0.95
            : null,
        constraints: BoxConstraints(
          maxWidth: _isExpanded.value ? double.infinity : 800,
          maxHeight: _isExpanded.value ? double.infinity : 700,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header محسن مع تباين أفضل
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                border: Border(
                  bottom: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.security,
                      color: theme.colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'صلاحيات ${widget.role.displayName}',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Obx(() {
                          final totalPermissions = _adminController.permissions.length;
                          final selectedCount = _selectedPermissions.length;
                          final percentage = totalPermissions > 0
                              ? (selectedCount / totalPermissions * 100).round()
                              : 0;

                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: selectedCount > 0
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.surfaceContainerHighest,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: selectedCount > 0
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.outline,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  selectedCount > 0 ? Icons.check_circle : Icons.radio_button_unchecked,
                                  color: selectedCount > 0
                                      ? AppColors.black
                                      : AppColors.accent,
                                  size: 14,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  '$selectedCount من $totalPermissions محدد ($percentage%)',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                  color: selectedCount > 0
                                      ? AppColors.textPrimary
                                      : theme.colorScheme.onSurfaceVariant,
                                  fontWeight: FontWeight.w600,
                                ),
                                ),
                              ],
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                  // زر التكبير/التصغير
                  Obx(() => IconButton(
                    onPressed: () => _isExpanded.value = !_isExpanded.value,
                    icon: Icon(
                      _isExpanded.value ? Icons.fullscreen_exit : Icons.fullscreen,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: theme.colorScheme.surface.withValues(alpha: 0.5),
                    ),
                    tooltip: _isExpanded.value ? 'تصغير النافذة' : 'تكبير النافذة',
                  )),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: Icon(
                      Icons.close,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: theme.colorScheme.surface.withValues(alpha: 0.5),
                    ),
                    tooltip: 'إغلاق',
                  ),
                ],
              ),
            ),

            // Content مع تحسين التخطيط
            Expanded(
              child: Obx(() {
                if (_isLoading.value) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'جاري تحميل صلاحيات الدور...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      // قسم الصلاحيات
                      Expanded(child: _buildPermissionsSection()),
                    ],
                  ),
                );
              }),
            ),

            // Actions محسنة مع تباين أفضل
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
                border: Border(
                  top: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () => Get.back(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      side: BorderSide(color: theme.colorScheme.outline),
                    ),
                    child: Text(
                      'إلغاء',
                      style: TextStyle(color: theme.colorScheme.onSurface),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Obx(() => ElevatedButton(
                    onPressed: _isSaving.value ? null : _savePermissions,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      elevation: 2,
                    ),
                    child: _isSaving.value
                        ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: theme.colorScheme.onPrimary,
                            ),
                          )
                        : Text(
                            'حفظ',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                  )),
                ],
              ),
            ),
          ],
        ),
      )),
    );
  }

  /// بناء قسم الصلاحيات
  Widget _buildPermissionsSection() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header محسن مع أزرار التحكم
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.security,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'الصلاحيات',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
                // أزرار التحكم محسنة
                Container(
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: theme.colorScheme.outline.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextButton.icon(
                        onPressed: _selectAllPermissions,
                        icon: Icon(
                          Icons.check_box,
                          color: theme.colorScheme.primary,
                          size: 18,
                        ),
                        label: Text(
                          'تحديد الكل',
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 24,
                        color: theme.colorScheme.outline.withValues(alpha: 0.3),
                      ),
                      TextButton.icon(
                        onPressed: _clearAllPermissions,
                        icon: Icon(
                          Icons.check_box_outline_blank,
                          color: theme.colorScheme.error,
                          size: 18,
                        ),
                        label: Text(
                          'إلغاء الكل',
                          style: TextStyle(
                            color: theme.colorScheme.error,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // عرض الصلاحيات
          Expanded(child: _buildPermissionsList()),
        ],
      ),
    );
  }

  /// بناء قائمة الصلاحيات
  Widget _buildPermissionsList() {
    return Obx(() {
      final permissions = _adminController.permissions;
      final theme = Theme.of(context);

      if (permissions.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Center(
            child: Column(
              children: [
                Icon(
                  Icons.security_outlined,
                  color: theme.colorScheme.onSurfaceVariant,
                  size: 48,
                ),
                const SizedBox(height: 12),
                Text(
                  'لا توجد صلاحيات متاحة',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        );
      }

      // تجميع الصلاحيات حسب المجموعة
      final groupedPermissions = <String, List<Permission>>{};
      for (final permission in permissions) {
        final group = permission.permissionGroup;
        groupedPermissions.putIfAbsent(group, () => []).add(permission);
      }

      return SingleChildScrollView(
        child: Column(
          children: groupedPermissions.entries.map((entry) {
            return _buildPermissionGroup(entry.key, entry.value);
          }).toList(),
        ),
      );
    });
  }

  /// بناء مجموعة صلاحيات
  Widget _buildPermissionGroup(String groupName, List<Permission> permissions) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        childrenPadding: const EdgeInsets.only(bottom: 8),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: theme.colorScheme.secondaryContainer,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.folder_outlined,
                color: theme.colorScheme.onSecondaryContainer,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                groupName,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Checkbox للمجموعة
            Obx(() {
              final selectedCount = permissions
                  .where((p) => _selectedPermissions.contains(p.id))
                  .length;
              final isAllSelected = selectedCount == permissions.length;
              final isPartialSelected = selectedCount > 0 && selectedCount < permissions.length;

              return Checkbox(
                value: isAllSelected ? true : (isPartialSelected ? null : false),
                tristate: true,
                activeColor: theme.colorScheme.primary,
                checkColor: theme.colorScheme.onPrimary,
                onChanged: (value) {
                  // تحديد الحالة الحالية
                  final currentSelectedCount = permissions
                      .where((p) => _selectedPermissions.contains(p.id))
                      .length;
                  final currentIsAllSelected = currentSelectedCount == permissions.length;

                  if (currentIsAllSelected) {
                    // إذا كانت كلها محددة، قم بإلغاء التحديد
                    for (final permission in permissions) {
                      _selectedPermissions.remove(permission.id);
                    }
                  } else {
                    // إذا لم تكن كلها محددة (جزئية أو فارغة)، قم بتحديد الكل
                    for (final permission in permissions) {
                      _selectedPermissions.add(permission.id);
                    }
                  }
                },
              );
            }),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4, right: 36),
          child: Obx(() {
            final selectedCount = permissions
                .where((p) => _selectedPermissions.contains(p.id))
                .length;
            final isAllSelected = selectedCount == permissions.length;
            final isPartialSelected = selectedCount > 0 && selectedCount < permissions.length;

            return Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: isAllSelected
                        ? theme.colorScheme.primary.withValues(alpha: 0.1)
                        : isPartialSelected
                            ? theme.colorScheme.tertiary.withValues(alpha: 0.1)
                            : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isAllSelected
                          ? theme.colorScheme.primary.withValues(alpha: 0.3)
                          : isPartialSelected
                              ? theme.colorScheme.tertiary.withValues(alpha: 0.3)
                              : theme.colorScheme.outline.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    '$selectedCount من ${permissions.length} محدد',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isAllSelected
                          ? theme.colorScheme.primary
                          : isPartialSelected
                              ? theme.colorScheme.tertiary
                              : theme.colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (isAllSelected) ...[
                  const SizedBox(width: 8),
                  Icon(
                    Icons.check_circle,
                    color: theme.colorScheme.primary,
                    size: 16,
                  ),
                ] else if (isPartialSelected) ...[
                  const SizedBox(width: 8),
                  Icon(
                    Icons.remove_circle,
                    color: theme.colorScheme.tertiary,
                    size: 16,
                  ),
                ],
              ],
            );
          }),
        ),
        children: permissions.map((permission) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Obx(() => CheckboxListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              title: Text(
                permission.name,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              subtitle: permission.description != null
                  ? Text(
                      permission.description!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    )
                  : null,
              value: _selectedPermissions.contains(permission.id),
              activeColor: theme.colorScheme.primary,
              checkColor: theme.colorScheme.onPrimary,
              onChanged: (value) {
                if (value == true) {
                  _selectedPermissions.add(permission.id);
                } else {
                  _selectedPermissions.remove(permission.id);
                }
              },
            )),
          );
        }).toList(),
      ),
    );
  }

  /// تحديد جميع الصلاحيات
  void _selectAllPermissions() {
    final allPermissionIds = _adminController.permissions.map((p) => p.id).toSet();
    _selectedPermissions.assignAll(allPermissionIds);
  }

  /// إلغاء تحديد جميع الصلاحيات
  void _clearAllPermissions() {
    _selectedPermissions.clear();
  }

  /// حفظ الصلاحيات
  Future<void> _savePermissions() async {
    // التحقق من وجود تغييرات
    if (_selectedPermissions.difference(_originalPermissions).isEmpty &&
        _originalPermissions.difference(_selectedPermissions).isEmpty) {
      Get.back();
      return;
    }

    _isSaving.value = true;

    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // تحديث صلاحيات الدور
      await _rolesApiService.grantMultiplePermissionsToRole(
        roleId: widget.role.id,
        permissionIds: _selectedPermissions.toList(),
        userId: currentUser.id,
        replaceExisting: true, // استبدال الصلاحيات الحالية
      );

      // إغلاق الحوار وإظهار رسالة نجاح
      Get.back();
      AdminMessageDialog.showSuccess(
        title: 'تم بنجاح',
        message: 'تم تحديث صلاحيات الدور بنجاح',
      );

      // استدعاء callback النجاح
      widget.onSuccess?.call();

    } catch (e) {
      debugPrint('خطأ في حفظ الصلاحيات: $e');
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في حفظ الصلاحيات: $e',
      );
    } finally {
      _isSaving.value = false;
    }
  }
}
