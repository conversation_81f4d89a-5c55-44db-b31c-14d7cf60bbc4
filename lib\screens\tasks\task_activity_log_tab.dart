import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/attachments_controller.dart';
import '../../models/task_history_models.dart';
import '../../utils/date_formatter.dart';
import '../../services/unified_permission_service.dart';

/// تبويب سجل النشاط للمهمة
class TaskActivityLogTab extends StatefulWidget {
  final String taskId;

  const TaskActivityLogTab({
    super.key,
    required this.taskId,
  });

  @override
  State<TaskActivityLogTab> createState() => _TaskActivityLogTabState();
}

class _TaskActivityLogTabState extends State<TaskActivityLogTab> {
  // الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final ScrollController _scrollController = ScrollController();
  String _selectedFilter = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadActivityLog();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadActivityLog() async {
    final taskController = Get.find<TaskController>();
    final taskIdInt = int.tryParse(widget.taskId);
    if (taskIdInt != null) {
      await taskController.loadTaskHistory(taskIdInt);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildFilterAndSearchBar(),
        Expanded(
          child: GetBuilder<TaskController>(
            id: 'task_history',
            builder: (controller) {
              final filteredHistory = _getFilteredHistory(controller.taskHistory);
              
              if (controller.isLoading) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'جاري تحميل سجل النشاط...',
                        style: AppStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                );
              }

              if (filteredHistory.isEmpty) {
                return _buildEmptyState();
              }

              return RefreshIndicator(
                onRefresh: _loadActivityLog,
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredHistory.length,
                  itemBuilder: (context, index) {
                    final historyItem = filteredHistory[index];
                    return _buildActivityLogItem(historyItem, index);
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFilterAndSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.card,
        border: Border(
          bottom: BorderSide(color: AppColors.getBorderColor()),
        ),
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            style: AppStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
            decoration: InputDecoration(
              hintText: 'البحث في سجل النشاط...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 12),
          // مرشحات النشاط
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('all', 'الكل'),
                const SizedBox(width: 8),
                _buildFilterChip('task_management', 'إدارة المهام'),
                const SizedBox(width: 8),
                _buildFilterChip('comments', 'التعليقات'),
                const SizedBox(width: 8),
                _buildFilterChip('attachments', 'المرفقات'),
                const SizedBox(width: 8),
                _buildFilterChip('documents', 'المستندات'),
                const SizedBox(width: 8),
                _buildFilterChip('progress', 'تحديث التقدم'),
                const SizedBox(width: 8),
                _buildFilterChip('time_tracking', 'تتبع الوقت'),
                const SizedBox(width: 8),
                _buildFilterChip('transfers', 'التحويلات'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return _permissionService.canFilterTasks()
        ? FilterChip(
            label: Text(label),
            selected: isSelected,
            onSelected: (selected) {
              setState(() {
                _selectedFilter = selected ? value : 'all';
              });
            },
            selectedColor: AppColors.primary.withAlpha(50),
            checkmarkColor: AppColors.primary,
          )
        : Chip(
            label: Text(label),
            backgroundColor: isSelected ? AppColors.primary.withAlpha(50) : null,
          );
  }

  Widget _buildEmptyState() {
    final hasFilter = _selectedFilter != 'all';
    final hasSearch = _searchQuery.isNotEmpty;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.textSecondary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              hasFilter || hasSearch ? Icons.search_off : Icons.history,
              size: 64,
              color: AppColors.textSecondary.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            hasFilter || hasSearch ? 'لا توجد نتائج' : 'لا يوجد سجل نشاط',
            style: AppStyles.headingMedium.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            hasFilter || hasSearch
              ? hasFilter && hasSearch
                ? 'لا توجد أنشطة تطابق البحث في فئة "${_getFilterDisplayName(_selectedFilter)}"'
                : hasFilter
                  ? 'لا توجد أنشطة من نوع "${_getFilterDisplayName(_selectedFilter)}"'
                  : 'لا توجد أنشطة تطابق البحث المحدد'
              : 'لم يتم تسجيل أي نشاط لهذه المهمة بعد',
            style: AppStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          if (hasFilter || hasSearch) ...[
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _selectedFilter = 'all';
                  _searchQuery = '';
                  _searchController.clear();
                });
              },
              icon: const Icon(Icons.clear_all),
              label: const Text('إزالة التصفية'),
            ),
            const SizedBox(height: 12),
          ],
          ElevatedButton.icon(
            onPressed: _loadActivityLog,
            icon: const Icon(Icons.refresh),
            label: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityLogItem(TaskHistory historyItem, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildActionIcon(historyItem.action),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildActionHeader(historyItem),
                        const SizedBox(height: 4),
                        _buildActionDescription(historyItem),
                        if (historyItem.details != null && historyItem.details!.isNotEmpty)
                          _buildActionDetails(historyItem.details!),
                        if (_hasValueChanges(historyItem))
                          _buildValueChanges(historyItem),
                        const SizedBox(height: 8),
                        _buildActionFooter(historyItem),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionIcon(String action) {
    IconData iconData;
    Color iconColor;

    switch (action.toLowerCase()) {
      case 'created':
      case 'إنشاء':
        iconData = Icons.add_circle;
        iconColor = AppColors.success;
        break;
      case 'updated':
      case 'تحديث':
        iconData = Icons.edit;
        iconColor = AppColors.info;
        break;
      case 'assigned':
      case 'تعيين':
        iconData = Icons.person_add;
        iconColor = AppColors.warning;
        break;
      case 'transferred':
      case 'تحويل_المهمة':
        iconData = Icons.swap_horiz;
        iconColor = AppColors.primary;
        break;
      case 'completed':
      case 'إكمال':
        iconData = Icons.check_circle;
        iconColor = AppColors.success;
        break;
      case 'status_changed':
      case 'تغيير_الحالة':
        iconData = Icons.swap_horiz;
        iconColor = AppColors.accent;
        break;
      case 'priority_changed':
      case 'تغيير_الأولوية':
        iconData = Icons.priority_high;
        iconColor = AppColors.error;
        break;
      case 'due_date_changed':
      case 'تغيير_تاريخ_الاستحقاق':
        iconData = Icons.schedule;
        iconColor = AppColors.warning;
        break;
      case 'comment_added':
      case 'إضافة_تعليق':
        iconData = Icons.comment;
        iconColor = AppColors.info;
        break;
      case 'comment_updated':
      case 'تعديل_تعليق':
        iconData = Icons.edit_note;
        iconColor = AppColors.primary;
        break;
      case 'comment_deleted':
      case 'حذف_تعليق':
        iconData = Icons.comment_outlined;
        iconColor = AppColors.error;
        break;
      case 'attachment_added':
      case 'إضافة_مرفق':
      case 'إضافة_مرفق_تحويل':
        iconData = Icons.attach_file;
        iconColor = AppColors.accent;
        break;
      case 'attachment_deleted':
      case 'حذف_مرفق':
        iconData = Icons.attachment_outlined;
        iconColor = AppColors.error;
        break;
      case 'attachment_deleted_permanent':
      case 'حذف_مرفق_نهائي':
        iconData = Icons.delete_forever;
        iconColor = AppColors.error;
        break;
      case 'time_tracking_started':
      case 'بدء_تتبع_الوقت':
        iconData = Icons.play_arrow;
        iconColor = AppColors.success;
        break;
      case 'time_tracking_stopped':
      case 'إيقاف_تتبع_الوقت':
        iconData = Icons.stop;
        iconColor = AppColors.warning;
        break;
      case 'document_created':
      case 'إنشاء_مستند':
        iconData = Icons.description;
        iconColor = AppColors.primary;
        break;
      case 'document_updated':
      case 'تحديث_مستند':
        iconData = Icons.edit_document;
        iconColor = AppColors.info;
        break;
      case 'document_deleted':
      case 'حذف_مستند':
        iconData = Icons.description_outlined;
        iconColor = AppColors.error;
        break;
      case 'progress_updated':
      case 'تحديث_التقدم':
      case 'تحديث_التقدم_السريع':
        iconData = Icons.trending_up;
        iconColor = AppColors.success;
        break;
      case 'deleted':
      case 'حذف':
        iconData = Icons.delete;
        iconColor = AppColors.error;
        break;
      default:
        iconData = Icons.info;
        iconColor = AppColors.textSecondary;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: iconColor.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  Widget _buildActionHeader(TaskHistory historyItem) {
    return Row(
      children: [
        Expanded(
          child: Text(
            historyItem.actionDescription,
            style: AppStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        if (historyItem.changeType != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(30),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              historyItem.changeType!,
              style: AppStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActionDescription(TaskHistory historyItem) {
    if (historyItem.changeDescription != null && historyItem.changeDescription!.isNotEmpty) {
      return Padding(
        padding: const EdgeInsets.only(top: 4),
        child: Text(
          historyItem.changeDescription!,
          style: AppStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildActionDetails(String details) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.border.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _buildParsedDetails(details),
    );
  }

  /// تحليل وعرض تفاصيل JSON بشكل مفهوم
  Widget _buildParsedDetails(String details) {
    try {
      // محاولة تحليل JSON
      if (details.startsWith('{')) {
        final detailsMap = json.decode(details) as Map<String, dynamic>;
        return _buildJsonDetails(detailsMap);
      }
    } catch (e) {
      // إذا فشل التحليل، عرض النص كما هو
      debugPrint('خطأ في تحليل details JSON: $e');
    }

    // عرض النص العادي
    return Text(
      details,
      style: AppStyles.bodySmall.copyWith(
        color: AppColors.textPrimary,
      ),
    );
  }

  /// بناء عرض تفاصيل JSON
  Widget _buildJsonDetails(Map<String, dynamic> detailsMap) {
    List<Widget> children = [];

    try {
      // عرض معلومات التحويل
      if (detailsMap.containsKey('newAssigneeId') || detailsMap.containsKey('previousAssigneeId')) {
        children.add(_buildTransferDetails(detailsMap));
      }

      // عرض الملاحظات
      if (detailsMap.containsKey('note') && detailsMap['note'] != null && detailsMap['note'].toString().isNotEmpty) {
        children.add(_buildNoteDetail(detailsMap['note'].toString()));
      }

      // عرض المرفقات
      if (detailsMap.containsKey('attachments') && detailsMap['attachments'] != null && detailsMap['attachments'].toString().isNotEmpty) {
        children.add(_buildAttachmentsDetail(detailsMap['attachments'].toString()));
      }

      // عرض معلومات المساهمة
      if (detailsMap.containsKey('contributionRecorded') && detailsMap['contributionRecorded'] == 'true') {
        children.add(_buildContributionDetail(detailsMap));
      }

      // عرض تفاصيل التعليقات
      if (detailsMap.containsKey('commentId')) {
        children.add(_buildCommentDetails(detailsMap));
      }

      // عرض تفاصيل المرفقات
      if (detailsMap.containsKey('attachmentId') && detailsMap.containsKey('fileName')) {
        children.add(_buildAttachmentDetails(detailsMap));
      }

      // عرض تفاصيل تتبع الوقت
      if (detailsMap.containsKey('entryId') && (detailsMap.containsKey('startTime') || detailsMap.containsKey('endTime'))) {
        children.add(_buildTimeTrackingDetails(detailsMap));
      }

      // عرض تفاصيل المستندات
      if (detailsMap.containsKey('documentId')) {
        children.add(_buildDocumentDetails(detailsMap));
      }

      // عرض تفاصيل تحديث التقدم
      if (detailsMap.containsKey('progressId') || detailsMap.containsKey('progressPercentage')) {
        children.add(_buildProgressDetails(detailsMap));
      }

      // عرض تفاصيل تغيير الحالة
      if (detailsMap.containsKey('oldStatus') && detailsMap.containsKey('newStatus')) {
        children.add(_buildStatusChangeDetails(detailsMap));
      }

      // عرض تفاصيل تغيير الأولوية
      if (detailsMap.containsKey('oldPriority') && detailsMap.containsKey('newPriority')) {
        children.add(_buildPriorityChangeDetails(detailsMap));
      }
    } catch (e) {
      debugPrint('خطأ في بناء تفاصيل JSON: $e');
      return Text(
        'خطأ في عرض التفاصيل',
        style: AppStyles.bodySmall.copyWith(
          color: AppColors.error,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    // إذا لم توجد تفاصيل معروفة، عرض JSON كما هو
    if (children.isEmpty) {
      return Text(
        detailsMap.toString(),
        style: AppStyles.bodySmall.copyWith(
          color: AppColors.textPrimary,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  /// بناء تفاصيل التحويل
  Widget _buildTransferDetails(Map<String, dynamic> detailsMap) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.swap_horiz, size: 16, color: AppColors.primary),
              const SizedBox(width: 4),
              Text(
                'تفاصيل التحويل:',
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          if (detailsMap.containsKey('previousAssigneeId'))
            FutureBuilder<String>(
              future: _getUserNameById(detailsMap['previousAssigneeId'].toString()),
              builder: (context, snapshot) {
                return Text(
                  'من: ${snapshot.data ?? 'مستخدم غير معروف'}',
                  style: AppStyles.bodySmall.copyWith(color: AppColors.textPrimary),
                );
              },
            ),
          if (detailsMap.containsKey('newAssigneeId'))
            FutureBuilder<String>(
              future: _getUserNameById(detailsMap['newAssigneeId'].toString()),
              builder: (context, snapshot) {
                return Text(
                  'إلى: ${snapshot.data ?? 'مستخدم غير معروف'}',
                  style: AppStyles.bodySmall.copyWith(color: AppColors.primary),
                );
              },
            ),
        ],
      ),
    );
  }

  /// بناء تفاصيل الملاحظة
  Widget _buildNoteDetail(String note) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.note, size: 16, color: AppColors.warning),
              const SizedBox(width: 4),
              Text(
                'ملاحظة:',
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.warning,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            note,
            style: AppStyles.bodySmall.copyWith(color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }

  /// بناء تفاصيل المرفقات
  Widget _buildAttachmentsDetail(String attachments) {
    final attachmentList = attachments.split(',').where((a) => a.trim().isNotEmpty).toList();

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.attach_file, size: 16, color: AppColors.success),
              const SizedBox(width: 4),
              Text(
                'مرفقات (${attachmentList.length}):',
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.success,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          ...attachmentList.map((attachment) => Padding(
            padding: const EdgeInsets.only(left: 20, bottom: 2),
            child: Text(
              '• ${attachment.trim()}',
              style: AppStyles.bodySmall.copyWith(color: Colors.grey[700]),
            ),
          )),
        ],
      ),
    );
  }

  /// بناء تفاصيل المساهمة
  Widget _buildContributionDetail(Map<String, dynamic> detailsMap) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.trending_up, size: 16, color: Colors.purple),
              const SizedBox(width: 4),
              Text(
                'مساهمة مسجلة:',
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.purple[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          if (detailsMap.containsKey('contributionPercentage'))
            Text(
              'نسبة المساهمة: ${detailsMap['contributionPercentage']}%',
              style: AppStyles.bodySmall.copyWith(color: Colors.grey[700]),
            ),
        ],
      ),
    );
  }

  bool _hasValueChanges(TaskHistory historyItem) {
    return (historyItem.oldValue != null && historyItem.oldValue!.isNotEmpty) ||
           (historyItem.newValue != null && historyItem.newValue!.isNotEmpty);
  }

  Widget _buildValueChanges(TaskHistory historyItem) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.info.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التغييرات:',
            style: AppStyles.bodySmall.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.info,
            ),
          ),
          const SizedBox(height: 4),
          if (historyItem.oldValue != null && historyItem.oldValue!.isNotEmpty)
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'من: ',
                  style: AppStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.error,
                  ),
                ),
                Expanded(
                  child: _buildValueDisplay(
                    historyItem.oldValue!,
                    isOldValue: true,
                    changeType: historyItem.changeType,
                  ),
                ),
              ],
            ),
          if (historyItem.newValue != null && historyItem.newValue!.isNotEmpty)
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إلى: ',
                  style: AppStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.success,
                  ),
                ),
                Expanded(
                  child: _buildValueDisplay(
                    historyItem.newValue!,
                    isOldValue: false,
                    changeType: historyItem.changeType,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  /// بناء عرض القيمة مع تحويل معرفات المستخدمين إلى أسماء
  Widget _buildValueDisplay(String value, {required bool isOldValue, String? changeType}) {
    // التحقق من كون القيمة معرف مرفق (للتغييرات من نوع attachment)
    if (changeType == 'attachment') {
      final attachmentId = int.tryParse(value);
      if (attachmentId != null && attachmentId > 0) {
        return FutureBuilder<String>(
          future: _getAttachmentNameById(value),
          builder: (context, snapshot) {
            final displayValue = snapshot.data ?? 'مرفق #$value';
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.attach_file,
                  size: 14,
                  color: isOldValue ? AppColors.error : AppColors.success,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    displayValue,
                    style: AppStyles.bodySmall.copyWith(
                      color: isOldValue ? AppColors.error : AppColors.success,
                      decoration: isOldValue ? TextDecoration.lineThrough : null,
                      fontWeight: isOldValue ? null : FontWeight.w500,
                    ),
                  ),
                ),
              ],
            );
          },
        );
      }
    }

    // التحقق من كون القيمة معرف مستخدم (رقم صغير فقط)
    final userId = int.tryParse(value);
    if (userId != null && userId > 0 && userId < 1000) { // فقط المعرفات الصغيرة (معرفات المستخدمين)
      return FutureBuilder<String>(
        future: _getUserNameById(value),
        builder: (context, snapshot) {
          final displayValue = snapshot.data ?? value;
          return Text(
            displayValue,
            style: AppStyles.bodySmall.copyWith(
              color: isOldValue ? AppColors.error : AppColors.success,
              decoration: isOldValue ? TextDecoration.lineThrough : null,
              fontWeight: isOldValue ? null : FontWeight.w500,
            ),
          );
        },
      );
    }

    // عرض القيمة العادية (أسماء المستخدمين أو أي نص آخر)
    return Text(
      value,
      style: AppStyles.bodySmall.copyWith(
        color: isOldValue ? AppColors.error : AppColors.success,
        decoration: isOldValue ? TextDecoration.lineThrough : null,
        fontWeight: isOldValue ? null : FontWeight.w500,
      ),
    );
  }

  /// الحصول على اسم المستخدم بواسطة المعرف
  Future<String> _getUserNameById(String userId) async {
    try {
      final userController = Get.find<UserController>();
      return await userController.getUserNameById(userId);
    } catch (e) {
      debugPrint('خطأ في الحصول على اسم المستخدم: $e');
      return 'مستخدم غير معروف';
    }
  }

  /// الحصول على اسم المرفق بواسطة المعرف
  Future<String> _getAttachmentNameById(String attachmentId) async {
    try {
      // التحقق من القيم الخاصة أولاً
      if (attachmentId.isEmpty || attachmentId == '0' || attachmentId == 'null') {
        return 'مرفق غير محدد';
      }

      // التحقق من كون القيمة رقم صحيح
      final id = int.tryParse(attachmentId);
      if (id == null || id <= 0) {
        return 'مرفق غير صحيح';
      }

      // ✅ استخدام AttachmentsController بدلاً من API مباشرة
      if (Get.isRegistered<AttachmentsController>()) {
        final attachmentsController = Get.find<AttachmentsController>();
        await attachmentsController.getAttachmentById(id);
        final attachment = attachmentsController.currentAttachment;

        if (attachment != null) {
          return attachment.fileName;
        } else {
          // إذا لم يوجد المرفق، قد يكون محذوف
          debugPrint('⚠️ المرفق $attachmentId غير موجود أو محذوف');
          return 'مرفق #$attachmentId (محذوف)';
        }
      } else {
        debugPrint('⚠️ AttachmentsController غير مسجل');
        return 'مرفق #$attachmentId (خطأ في النظام)';
      }
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على اسم المرفق $attachmentId: $e');
      return 'مرفق #$attachmentId (خطأ)';
    }
  }

  Widget _buildActionFooter(TaskHistory historyItem) {
    return Row(
      children: [
        Icon(
          Icons.person,
          size: 16,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          _getUserName(historyItem),
          style: AppStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const Spacer(),
        Icon(
          Icons.access_time,
          size: 16,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          _formatTimestamp(historyItem.timestampDateTime),
          style: AppStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _getUserName(TaskHistory historyItem) {
    if (historyItem.changedByNavigation != null) {
      return historyItem.changedByNavigation!.name;
    } else if (historyItem.user != null) {
      return historyItem.user!.name;
    }
    return 'النظام';
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return DateFormatter.formatDate(timestamp);
    }
  }

  List<TaskHistory> _getFilteredHistory(RxList<TaskHistory> history) {
    var filtered = history.where((item) {
      // تطبيق مرشح النوع
      if (_selectedFilter != 'all' && !_matchesFilter(item.action, _selectedFilter)) {
        return false;
      }

      // تطبيق البحث النصي
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return item.actionDescription.toLowerCase().contains(query) ||
               (item.details?.toLowerCase().contains(query) ?? false) ||
               (item.changeDescription?.toLowerCase().contains(query) ?? false) ||
               (item.oldValue?.toLowerCase().contains(query) ?? false) ||
               (item.newValue?.toLowerCase().contains(query) ?? false);
      }

      return true;
    }).toList();

    // ترتيب حسب التاريخ (الأحدث أولاً)
    filtered.sort((a, b) => b.timestampDateTime.compareTo(a.timestampDateTime));

    return filtered;
  }

  /// التحقق من تطابق الأكشن مع الفلتر (يدعم العربية والإنجليزية)
  bool _matchesFilter(String action, String filter) {
    final actionLower = action.toLowerCase();
    final filterLower = filter.toLowerCase();

    // مطابقة مباشرة
    if (actionLower == filterLower) return true;

    // مطابقة الأكشن مع الفلاتر المجمعة
    switch (filterLower) {
      // إدارة المهام (إنشاء، تحديث، تعيين، إكمال، تغيير الحالة، تغيير الأولوية)
      case 'task_management':
        return actionLower == 'created' || actionLower == 'إنشاء' ||
               actionLower == 'updated' || actionLower == 'تحديث' ||
               actionLower == 'assigned' || actionLower == 'تعيين' ||
               actionLower == 'completed' || actionLower == 'إكمال' ||
               actionLower == 'status_changed' || actionLower == 'تغيير_الحالة' ||
               actionLower == 'priority_changed' || actionLower == 'تغيير_الأولوية';

      // التعليقات (إضافة، تعديل، حذف)
      case 'comments':
        return actionLower == 'comment_added' || actionLower == 'إضافة_تعليق' ||
               actionLower == 'comment_updated' || actionLower == 'تعديل_تعليق' ||
               actionLower == 'comment_deleted' || actionLower == 'حذف_تعليق';

      // المرفقات (إضافة، حذف)
      case 'attachments':
        return actionLower == 'attachment_added' || actionLower == 'إضافة_مرفق' || actionLower == 'إضافة_مرفق_تحويل' ||
               actionLower == 'attachment_deleted' || actionLower == 'حذف_مرفق' || actionLower == 'حذف_مرفق_نهائي';

      // المستندات (إنشاء، تحديث، حذف)
      case 'documents':
        return actionLower == 'document_created' || actionLower == 'إنشاء_مستند' ||
               actionLower == 'document_updated' || actionLower == 'تحديث_مستند' ||
               actionLower == 'document_deleted' || actionLower == 'حذف_مستند';

      // تحديث التقدم
      case 'progress':
        return actionLower == 'progress_updated' || actionLower == 'تحديث_التقدم' || actionLower == 'تحديث_التقدم_السريع';

      // تتبع الوقت (بدء، إيقاف)
      case 'time_tracking':
        return actionLower == 'time_tracking_started' || actionLower == 'بدء_تتبع_الوقت' ||
               actionLower == 'time_tracking_stopped' || actionLower == 'إيقاف_تتبع_الوقت';

      // التحويلات
      case 'transfers':
        return actionLower == 'transferred' || actionLower == 'تحويل_المهمة';

      default:
        return false;
    }
  }

  /// بناء تفاصيل التعليقات
  Widget _buildCommentDetails(Map<String, dynamic> detailsMap) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.teal.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.teal.withAlpha(50)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.comment, size: 16, color: Colors.teal),
              const SizedBox(width: 8),
              Text(
                'تفاصيل التعليق',
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.teal,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (detailsMap.containsKey('contentPreview'))
            Text(
              'المحتوى: ${detailsMap['contentPreview']}',
              style: AppStyles.bodySmall,
            ),
          if (detailsMap.containsKey('oldContentPreview') && detailsMap.containsKey('newContentPreview'))
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'المحتوى السابق: ${detailsMap['oldContentPreview']}',
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'المحتوى الجديد: ${detailsMap['newContentPreview']}',
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          if (detailsMap.containsKey('deletedContentPreview'))
            Text(
              'المحتوى المحذوف: ${detailsMap['deletedContentPreview']}',
              style: AppStyles.bodySmall.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
        ],
      ),
    );
  }

  /// بناء تفاصيل المرفقات
  Widget _buildAttachmentDetails(Map<String, dynamic> detailsMap) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.indigo.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.indigo.withAlpha(50)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.attach_file, size: 16, color: Colors.indigo),
              const SizedBox(width: 8),
              Text(
                'تفاصيل المرفق',
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'اسم الملف: ${detailsMap['fileName']}',
            style: AppStyles.bodySmall,
          ),
          if (detailsMap.containsKey('fileSize'))
            Text(
              'حجم الملف: ${_formatFileSize(detailsMap['fileSize'])}',
              style: AppStyles.bodySmall,
            ),
          if (detailsMap.containsKey('fileType'))
            Text(
              'نوع الملف: ${detailsMap['fileType']}',
              style: AppStyles.bodySmall,
            ),
          if (detailsMap.containsKey('permanent') && detailsMap['permanent'] == true)
            Text(
              'تم الحذف نهائياً من القرص',
              style: AppStyles.bodySmall.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );
  }

  /// بناء تفاصيل تتبع الوقت
  Widget _buildTimeTrackingDetails(Map<String, dynamic> detailsMap) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withAlpha(50)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.timer, size: 16, color: Colors.green),
              const SizedBox(width: 8),
              Text(
                'تفاصيل تتبع الوقت',
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (detailsMap.containsKey('description') && detailsMap['description'] != null)
            Text(
              'الوصف: ${detailsMap['description']}',
              style: AppStyles.bodySmall,
            ),
          if (detailsMap.containsKey('startTime'))
            Text(
              'وقت البداية: ${_formatTimestampFromDetails(detailsMap['startTime'])}',
              style: AppStyles.bodySmall,
            ),
          if (detailsMap.containsKey('endTime'))
            Text(
              'وقت النهاية: ${_formatTimestampFromDetails(detailsMap['endTime'])}',
              style: AppStyles.bodySmall,
            ),
          if (detailsMap.containsKey('duration'))
            Text(
              'المدة: ${_formatDuration(detailsMap['duration'])}',
              style: AppStyles.bodySmall.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
        ],
      ),
    );
  }

  /// تنسيق المدة بالثواني إلى نص مقروء
  String _formatDuration(dynamic duration) {
    if (duration == null) return 'غير محدد';

    int seconds = duration is int ? duration : int.tryParse(duration.toString()) ?? 0;

    if (seconds < 60) {
      return '$seconds ثانية';
    } else if (seconds < 3600) {
      int minutes = seconds ~/ 60;
      int remainingSeconds = seconds % 60;
      return '$minutes دقيقة${remainingSeconds > 0 ? ' و $remainingSeconds ثانية' : ''}';
    } else {
      int hours = seconds ~/ 3600;
      int remainingMinutes = (seconds % 3600) ~/ 60;
      return '$hours ساعة${remainingMinutes > 0 ? ' و $remainingMinutes دقيقة' : ''}';
    }
  }

  /// تنسيق حجم الملف إلى وحدات مقروءة
  String _formatFileSize(dynamic fileSize) {
    if (fileSize == null) return 'غير محدد';

    double bytes = fileSize is double ? fileSize : double.tryParse(fileSize.toString()) ?? 0;

    if (bytes < 1024) {
      return '${bytes.toInt()} بايت';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} جيجابايت';
    }
  }

  /// تنسيق التوقيت من تفاصيل JSON مع معالجة الأخطاء
  String _formatTimestampFromDetails(dynamic timestamp) {
    try {
      if (timestamp == null) return 'غير محدد';

      int timestampInt = timestamp is int ? timestamp : int.tryParse(timestamp.toString()) ?? 0;

      if (timestampInt <= 0) return 'غير صحيح';

      // التحقق من كون التوقيت بالثواني أم بالـ milliseconds
      // إذا كان الرقم كبير جداً، فهو بالـ milliseconds
      DateTime dateTime;
      if (timestampInt > 1000000000000) {
        // بالـ milliseconds
        dateTime = DateTime.fromMillisecondsSinceEpoch(timestampInt);
      } else {
        // بالثواني
        dateTime = DateTime.fromMillisecondsSinceEpoch(timestampInt * 1000);
      }

      return DateFormatter.formatDateTime(dateTime);
    } catch (e) {
      debugPrint('خطأ في تنسيق التوقيت: $e');
      return 'خطأ في التوقيت';
    }
  }

  /// الحصول على اسم الفلتر بالعربية
  String _getFilterDisplayName(String filter) {
    switch (filter) {
      case 'all':
        return 'جميع العمليات';
      case 'task_management':
        return 'إدارة المهام';
      case 'comments':
        return 'التعليقات';
      case 'attachments':
        return 'المرفقات';
      case 'documents':
        return 'المستندات';
      case 'progress':
        return 'تحديث التقدم';
      case 'time_tracking':
        return 'تتبع الوقت';
      case 'transfers':
        return 'التحويلات';
      default:
        return filter;
    }
  }

  /// بناء عرض تفاصيل المستندات
  Widget _buildDocumentDetails(Map<String, dynamic> detailsMap) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withAlpha(50)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.description, size: 16, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                'تفاصيل المستند',
                style: AppStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (detailsMap.containsKey('documentTitle'))
            Text(
              'العنوان: ${detailsMap['documentTitle']}',
              style: AppStyles.bodySmall,
            ),
          if (detailsMap.containsKey('documentType'))
            Text(
              'النوع: ${detailsMap['documentType']}',
              style: AppStyles.bodySmall,
            ),
          if (detailsMap.containsKey('oldTitle') && detailsMap.containsKey('newTitle'))
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'العنوان السابق: ${detailsMap['oldTitle']}',
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'العنوان الجديد: ${detailsMap['newTitle']}',
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  /// بناء عرض تفاصيل تحديث التقدم
  Widget _buildProgressDetails(Map<String, dynamic> detailsMap) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withAlpha(50)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.trending_up, size: 16, color: AppColors.success),
              const SizedBox(width: 8),
              Text(
                'تفاصيل تحديث التقدم',
                style: AppStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.success,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (detailsMap.containsKey('progressPercentage'))
            Text(
              'نسبة التقدم: ${detailsMap['progressPercentage']}%',
              style: AppStyles.bodySmall.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
          if (detailsMap.containsKey('notes') && detailsMap['notes'].toString().isNotEmpty)
            Text(
              'الملاحظات: ${detailsMap['notes']}',
              style: AppStyles.bodySmall,
            ),
          if (detailsMap.containsKey('isQuickUpdate') && detailsMap['isQuickUpdate'] == true)
            Text(
              'نوع التحديث: تحديث سريع',
              style: AppStyles.bodySmall.copyWith(
                fontStyle: FontStyle.italic,
                color: Colors.grey[600],
              ),
            ),
        ],
      ),
    );
  }

  /// بناء عرض تفاصيل تغيير الحالة
  Widget _buildStatusChangeDetails(Map<String, dynamic> detailsMap) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.purple.withAlpha(50)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.swap_horiz, size: 16, color: Colors.purple[600]),
              const SizedBox(width: 8),
              Text(
                'تفاصيل تغيير الحالة',
                style: AppStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.purple[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الحالة السابقة:',
                      style: AppStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red.withAlpha(20),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.red.withAlpha(50)),
                      ),
                      child: Text(
                        _translateStatus(detailsMap['oldStatus']?.toString() ?? ''),
                        style: AppStyles.bodySmall.copyWith(
                          color: Colors.red[700],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Icon(Icons.arrow_forward, color: Colors.grey[600], size: 20),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الحالة الجديدة:',
                      style: AppStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withAlpha(20),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.green.withAlpha(50)),
                      ),
                      child: Text(
                        _translateStatus(detailsMap['newStatus']?.toString() ?? ''),
                        style: AppStyles.bodySmall.copyWith(
                          color: Colors.green[700],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عرض تفاصيل تغيير الأولوية
  Widget _buildPriorityChangeDetails(Map<String, dynamic> detailsMap) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withAlpha(50)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.priority_high, size: 16, color: Colors.orange[600]),
              const SizedBox(width: 8),
              Text(
                'تفاصيل تغيير الأولوية',
                style: AppStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الأولوية السابقة:',
                      style: AppStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red.withAlpha(20),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.red.withAlpha(50)),
                      ),
                      child: Text(
                        _translatePriority(detailsMap['oldPriority']?.toString() ?? ''),
                        style: AppStyles.bodySmall.copyWith(
                          color: Colors.red[700],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Icon(Icons.arrow_forward, color: Colors.grey[600], size: 20),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الأولوية الجديدة:',
                      style: AppStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withAlpha(20),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.green.withAlpha(50)),
                      ),
                      child: Text(
                        _translatePriority(detailsMap['newPriority']?.toString() ?? ''),
                        style: AppStyles.bodySmall.copyWith(
                          color: Colors.green[700],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// ترجمة حالة المهمة إلى العربية
  String _translateStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'قيد الانتظار';
      case 'in_progress':
      case 'inprogress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'waiting_for_info':
        return 'في انتظار معلومات';
      default:
        return status;
    }
  }

  /// ترجمة أولوية المهمة إلى العربية
  String _translatePriority(String priority) {
    switch (priority.toLowerCase()) {
      case 'low':
        return 'منخفضة';
      case 'medium':
        return 'متوسطة';
      case 'high':
        return 'عالية';
      case 'urgent':
        return 'عاجلة';
      default:
        return priority;
    }
  }
}