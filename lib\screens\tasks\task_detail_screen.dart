import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/auth_controller.dart';
import 'package:flutter_application_2/core/function.dart';
import 'package:flutter_application_2/professional_reports/core.dart';
import 'package:flutter_application_2/professional_reports/reporttask.dart';
import 'package:flutter_application_2/professional_reports/simple_comprehensive_task_report_service.dart';
import 'package:flutter_application_2/screens/tasks/contributors_tab.dart';
import 'package:flutter_application_2/screens/tasks/task_activity_log_tab.dart';
import 'package:flutter_application_2/services/export_services/enhanced_pdf_export_service.dart';
import 'package:get/get.dart';
// import 'package:flutter_application_2/professional_reports/reporttask.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../config/api_config.dart';
import '../../models/task_model.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/task_messages_controller.dart';
import '../../services/api/task_messages_api_service.dart';
import '../../controllers/attachments_controller.dart'; // إضافة استيراد AttachmentsController
import '../../utils/signalr_diagnostics.dart';
import '../../controllers/text_document_controller.dart'; // <-- إضافة استيراد TextDocumentController
import '../../services/unified_permission_service.dart';
// import '../../services/enhanced_pdf_export_service.dart'; // <-- إضافة استيراد الخدمة الجديدة
import '../../utils/date_formatter.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/task_detail_helpers.dart';
import '../widgets/draggable_divider.dart';
import '../widgets/task_transfer_button.dart';
import 'simple_task_comments_tab.dart'; // استخدام النسخة المبسطة مباشرة

import 'inline_edit_task_screen.dart';
import 'subtasks_tab.dart';
import 'task_progress_tab.dart';
import 'task_attachments_tab.dart';
import 'task_transfer_history_tab.dart';
import 'task_overview_tab.dart';
 // التبويب الجديد البسيط
import 'simple_contributor_contributions_tab.dart'; // تبويب المساهمات المبسط
import 'enhanced_task_documents_tab.dart';
// تبويب سجل النشاط المحسن
import '../../controllers/task_comments_controller.dart'; // إضافة استيراد TaskCommentsController
import '../../services/unified_signalr_service.dart';

class TaskDetailScreen extends StatefulWidget {
  final String taskId;
  final int? initialTabIndex;

  const TaskDetailScreen({
    super.key,
    required this.taskId,
    this.initialTabIndex,
  });

  @override
  State<TaskDetailScreen> createState() => _TaskDetailScreenState();
}

class _TaskDetailScreenState extends State<TaskDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _commentController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _progressController = TextEditingController();
  final ScrollController _messageScrollController = ScrollController();
  bool _showSidebar = true;

  // خدمة الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  // متغيرات إشعارات الكتابة
  bool _isTyping = false;
  Timer? _typingTimer;

  // إزالة المتغير غير المستخدم
  // Map<String, dynamic>? _replyToMessage; // تمت الإزالة

  double _leftPanelFlex = 3;
  double _rightPanelFlex = 2;
  final double _minPanelFlex = 1;
  final double _maxPanelFlex = 5;

  static const int _subtasksTabIndex = 2;
  // static const int _commentsTabIndex = 6; // لم يعد مستخدمًا
  static const int _conversationTabIndex = 7;
  static const int _contributorsTabIndex = 10;
  // إزالة المؤشر غير المستخدم
  static const int _totalTabsCount = 12;

  @override
  void initState() {
    super.initState();

    // 🔒 فحص الصلاحيات في بداية الشاشة - طبقة حماية أساسية
    if (!_permissionService.canViewTaskDetails()) {
      // إظهار رسالة خطأ والعودة للصفحة السابقة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.snackbar(
          'غير مسموح',
          'ليس لديك صلاحية لعرض تفاصيل المهام',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withAlpha(51),
          colorText: AppColors.error,
          duration: const Duration(seconds: 3),
        );
        Get.back(); // العودة للصفحة السابقة
      });
      return;
    }

    // التأكد من تهيئة AttachmentsController قبل استخدامه
    if (!Get.isRegistered<AttachmentsController>()) {
      Get.put(AttachmentsController(), permanent: true);
    }

    // التأكد من تهيئة TextDocumentController قبل استخدامه
    Get.put(TextDocumentController());
    // تهيئة EnhancedPdfExportService
    Get.put(EnhancedPdfExportService());
    // تهيئة TaskMessagesController
    final messagesController = Get.put(TaskMessagesController());
    // تمرير ScrollController إلى المتحكم للتمرير التلقائي
    messagesController.setScrollController(_messageScrollController);

    // تهيئة TaskCommentsController
    if (!Get.isRegistered<TaskCommentsController>()) {
      Get.put(TaskCommentsController());
    }

    _tabController = TabController(
      length: _totalTabsCount,
      vsync: this,
      initialIndex: widget.initialTabIndex ?? 0,
    );
    _tabController.addListener(() => setState(() {}));
    Get.put<TabController>(_tabController, tag: 'task_detail_tabs');

    // ✅ إضافة SignalR للتحديثات الفورية (نفس آلية tasks_tab.dart)
    _setupSignalRListeners();

    _loadTaskDetails();
  }

  /// إعداد مستمعات SignalR للتحديثات الفورية (نفس آلية tasks_tab.dart)
  void _setupSignalRListeners() {
    try {
      final signalRService = Get.find<UnifiedSignalRService>();

      // الاستماع لتحديثات المهام (نفس الكود من tasks_tab.dart)
      signalRService.chatHubConnection?.on("TaskUpdated", (arguments) {
        debugPrint("SignalR: Task Updated received in detail screen: $arguments");
        // ✅ إعادة تحميل البيانات من الخادم مباشرة (نفس tasks_tab.dart)
        _reloadTaskData();
      });

      signalRService.hubConnection?.on("TaskStatusUpdated", (arguments) {
        debugPrint("SignalR: Task Status Updated received in detail screen: $arguments");
        // ✅ إعادة تحميل البيانات من الخادم مباشرة (نفس tasks_tab.dart)
        _reloadTaskData();
      });

      debugPrint('✅ تم إعداد مستمعات SignalR في صفحة تفاصيل المهمة');
    } catch (e) {
      debugPrint('⚠️ خطأ في إعداد مستمعات SignalR: $e');
    }
  }

  /// إعادة تحميل بيانات المهمة من الخادم (نفس آلية tasks_tab.dart)
  Future<void> _reloadTaskData() async {
    try {
      final taskController = Get.find<TaskController>();

      // ✅ إعادة تحميل البيانات من الخادم مع forceRefresh: true (نفس tasks_tab.dart)
      await taskController.loadTaskById(int.parse(widget.taskId), forceRefresh: true);

      // ✅ إجبار تحديث الواجهة باستخدام setState (نفس tasks_tab.dart)
      if (mounted) {
        setState(() {});
      }

      debugPrint('✅ تم إعادة تحميل بيانات المهمة بنجاح');
    } catch (e) {
      debugPrint('⚠️ خطأ في إعادة تحميل بيانات المهمة: $e');
    }
  }

  @override
  void dispose() {
    // مغادرة محادثة المهمة عبر SignalR
    final taskIdInt = int.tryParse(widget.taskId);
    if (taskIdInt != null) {
      try {
        final messagesController = Get.find<TaskMessagesController>();
        messagesController.leaveTaskConversation(taskIdInt);
      } catch (e) {
        // تجاهل الخطأ إذا كان المتحكم غير موجود
      }
    }

    _tabController.dispose();
    _commentController.dispose();
    _messageController.dispose();
    _progressController.dispose();
    _messageScrollController.dispose();

    // إلغاء مؤقت الكتابة وإرسال إشعار توقف الكتابة
    _typingTimer?.cancel();
    if (_isTyping) {
      _sendStoppedTypingNotification();
    }

    Get.delete<TabController>(tag: 'task_detail_tabs');
    super.dispose();
  }

  /// معالجة إشعارات الكتابة
  void _handleTypingNotification(String text) {
    final currentUser = Get.find<AuthController>().currentUser.value;

    if (currentUser == null) return;

    // إذا كان النص فارغًا، نرسل إشعار توقف الكتابة
    if (text.isEmpty) {
      if (_isTyping) {
        _sendStoppedTypingNotification();
      }
      return;
    }

    // إذا لم يكن المستخدم يكتب بالفعل، نرسل إشعار بدء الكتابة
    if (!_isTyping) {
      _sendTypingNotification();
    }

    // إعادة ضبط المؤقت في كل مرة يتغير فيها النص
    _typingTimer?.cancel();
    _typingTimer = Timer(const Duration(seconds: 3), () {
      // بعد 3 ثوانٍ من عدم الكتابة، نرسل إشعار توقف الكتابة
      _sendStoppedTypingNotification();
    });
  }

  /// إرسال إشعار بدء الكتابة
  void _sendTypingNotification() {
    final taskIdInt = int.parse(widget.taskId);
    final currentUser = Get.find<AuthController>().currentUser.value;

    if (currentUser == null) return;

    _isTyping = true;
    final messagesController = Get.find<TaskMessagesController>();
    messagesController.sendTypingIndicator(taskIdInt, currentUser.name);
  }

  /// إرسال إشعار توقف الكتابة
  void _sendStoppedTypingNotification() {
    final taskIdInt = int.parse(widget.taskId);
    final currentUser = Get.find<AuthController>().currentUser.value;

    if (currentUser == null) return;

    _isTyping = false;
    final messagesController = Get.find<TaskMessagesController>();
    messagesController.sendStoppedTypingIndicator(taskIdInt, currentUser.name);
  }

  Future<void> _loadTaskDetails() async {
    final taskController = Get.find<TaskController>();
    try {
      await taskController.loadTaskDetails(widget.taskId);

      // تحميل المساهمين للمهمة
      final taskIdInt = int.tryParse(widget.taskId);
      if (taskIdInt != null) {
        await taskController.loadTaskContributors(taskIdInt);

        // تحميل رسائل المهمة
        final messagesController = Get.find<TaskMessagesController>();
        await messagesController.loadTaskMessages(taskIdInt);
        await messagesController.loadPinnedMessages(taskIdInt);

        // الانضمام لمحادثة المهمة عبر SignalR
        await messagesController.joinTaskConversation(taskIdInt);

        // تحميل تاريخ المهمة
        await taskController.loadTaskHistory(taskIdInt);
      }

      // تم إزالة استدعاء update هنا لتجنب الخطأ
    } catch (e) {
      Get.snackbar('خطأ', 'خطأ في تحميل التفاصيل: ${e.toString()}');
    }

    // إجبار تحديث الواجهة بعد تحميل البيانات
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
  
    final isLargeScreen = ResponsiveHelper.isLargeScreen(context);

    return Scaffold(
      appBar: AppBar(
        title: GetBuilder<TaskController>(
          builder: (controller) => Row(
            children: [
              // رقم المهمة - Task ID
              if (controller.currentTask != null) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(26),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: AppColors.primary.withAlpha(77)),
                  ),
                  child: Text(
                    '#${controller.currentTask!.id}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
              ],
              Expanded(
                flex: 3, // إعطاء مساحة أكبر للعنوان
                child: Text(
                  controller.currentTask?.title ?? 'taskDetails'.tr,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 4), // تقليل المساحة
              // مؤشر حالة الاتصال المحسن - مضغوط
              Flexible(
                child: GetBuilder<TaskMessagesController>(
                  builder: (messagesController) {
                    final signalRService = Get.find<UnifiedSignalRService>();

                    // فحص حالة جميع الاتصالات
                    final isChatConnected = signalRService.isChatHubConnected;
                    final isTaskHubConnected = signalRService.isTaskHubConnected;
                    final isCommentsConnected = signalRService.isTaskCommentsHubConnected;
                    final isNotificationConnected = signalRService.isNotificationHubConnected;

                    // تحديد الحالة العامة
                    final isFullyConnected = signalRService.isFullyConnected;
                    final hasPartialConnection = isChatConnected || isTaskHubConnected || isCommentsConnected || isNotificationConnected;

                    Color statusColor;

                    if (isFullyConnected) {
                      statusColor = AppColors.success;
                    } else if (hasPartialConnection) {
                      statusColor = AppColors.warning;
                    } else {
                      statusColor = AppColors.error;
                    }

                    return GestureDetector(
                      onTap: () {
                        // عرض تفاصيل الاتصال عند النقر
                        _showConnectionDetails(signalRService);
                      },
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: statusColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: _buildAppBarActions(isLargeScreen),
      ),
      body: GetBuilder<TaskController>(
        id: 'task_details',
        builder: (controller) {
          // if (controller.isLoading) {
          //   return const Center(
          //     child: Column(
          //       mainAxisAlignment: MainAxisAlignment.center,
          //       children: [
          //         Icon(Icons.hourglass_empty, size: 48, color: AppColors.primary),
          //         SizedBox(height: 16),
          //         Text(
          //           'جاري تحميل تفاصيل المهمة...',
          //           style: TextStyle(fontSize: 16, color: AppColors.primary),
          //         ),
          //       ],
          //     ),
          //   );
          // }

          if (controller.error.isNotEmpty) {
            return _buildErrorWidget(controller.error);
          }

          if (controller.currentTask == null) {
            return _buildErrorWidget('لم يتم العثور على المهمة');
          }

          final task = controller.currentTask!;
          return isLargeScreen
              ? _buildLargeScreenLayout(task)
              : _buildSmallScreenLayout(task);
        },
      ),
    );
  }

  List<Widget> _buildAppBarActions(bool isLargeScreen) {
    return [
      // زر تشخيص SignalR
      if (_permissionService.canAccessTesting())
        IconButton(
          icon: const Icon(Icons.network_check),
          tooltip: 'تشخيص SignalR',
          onPressed: () => SignalRDiagnostics.showDiagnosticsDialog(),
        ),
      if (isLargeScreen)
        IconButton(
          icon: Icon(_showSidebar ? Icons.chevron_right : Icons.chevron_left),
          tooltip: _showSidebar ? 'hideSidebar'.tr : 'showSidebar'.tr,
          onPressed: () => setState(() => _showSidebar = !_showSidebar),
        ),
      if (_permissionService.canEditTask())
        IconButton(
          icon: const Icon(Icons.edit),
          tooltip: 'editTask'.tr,
          onPressed: () {
            Get.to(() => InlineEditTaskScreen(taskId: widget.taskId))
                ?.then((_) => _loadTaskDetails());
          },
        ),
      // زر تقرير المهمة
      GetBuilder<TaskController>(
        builder: (controller) {
          final task = controller.currentTask;
          if (task == null) return const SizedBox.shrink();
          return _permissionService.canExportPdfReports()
              ? IconButton(
                  icon: Icon(Icons.picture_as_pdf, color: AppColors.error),
                  tooltip: 'تقرير المهمة',
                  onPressed: () async {
                    await showPdfReport(
          generatePdf: () => generateSingleTaskReportPdf(task),
          fileNamePrefix: 'تقرير_مهمة_${task.id}',
        );

                    // Get.dialog(const Center(child: CircularProgressIndicator()), barrierDismissible: false);
                    // try {
                    //   final pdfDoc = await generateSingleTaskReportPdf(task);
                    //   final bytes = await pdfDoc.save();
                    //   final dir = await getTemporaryDirectory();
                    //   final file = File('${dir.path}/task_report_${task.id}.pdf');
                    //   await file.writeAsBytes(bytes);
                    //   Get.back();
                    //   await OpenFile.open(file.path);
                    // } catch (e) {
                    //   Get.back();
                    //   Get.snackbar('خطأ', 'فشل في توليد أو فتح التقرير: $e', backgroundColor: AppColors.statusCancelled.withValues(alpha: 0.1), colorText: AppColors.statusCancelled);
                    // }
                  },
                )
              : const SizedBox.shrink();
        },
      ),

      // زر التقرير الشامل الجديد
      GetBuilder<TaskController>(
        builder: (controller) {
          final task = controller.currentTask;
          if (task == null) return const SizedBox.shrink();
          return _permissionService.canExportPdfReports()
              ? PopupMenuButton<String>(
                  icon: Icon(Icons.assessment, color: AppColors.primary),
                  tooltip: 'التقرير الشامل',
                  onSelected: (value) async {
                    if (value == 'full') {
                      await _generateComprehensiveTaskReport(task);
                    } else if (value == 'filtered') {
                      await _generateFilteredComprehensiveTaskReport(task);
                    }
                  },
                  itemBuilder: (context) => [
                     PopupMenuItem<String>(
                      value: 'full',
                      child: Row(
                        children: [
                          Icon(Icons.description, color: AppColors.info),
                          SizedBox(width: 8),
                          Text('التقرير الكامل', style: TextStyle(color: AppColors.textPrimary)),
                        ],
                      ),
                    ),
                     PopupMenuItem<String>(
                      value: 'filtered',
                      child: Row(
                        children: [
                          Icon(Icons.filter_list, color: AppColors.success),
                          SizedBox(width: 8),
                          Text('التقرير مع فلتر',
                           style: AppStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),)
                        ],
                      ),
                    ),
                  ],
                )
              : const SizedBox.shrink();
        },
      ),
      // 🔒 زر تحويل المهمة - محمي بصلاحية
       if (_permissionService.canTransferTask())
        GetBuilder<TaskController>(
          builder: (controller) {
            final task = controller.currentTask;
            if (task == null) return const SizedBox.shrink();
            return TaskTransferButton(
              task: task,
              onTransfer: (String userId, String comment, List<String> attachments) async {
                final taskController = Get.find<TaskController>();
                final userIdInt = int.tryParse(userId);

                if (userIdInt != null) {
                  // عرض مؤشر التحميل
                  Get.dialog(
                    const Center(child: CircularProgressIndicator()),
                    barrierDismissible: false,
                  );

                  // تنفيذ عملية التحويل
                  final result = await taskController.transferTask(
                    task.id,
                    userIdInt,
                    comment,
                    attachments,
                  );

                  // إغلاق مؤشر التحميل
                  Get.back();

                  if (result) {
                    // عرض رسالة نجاح
                    Get.snackbar(
                      'تم بنجاح',
                      'تم تحويل المهمة بنجاح',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: AppColors.success.withAlpha(51),
                      colorText: AppColors.success,
                      duration: const Duration(seconds: 3),
                    );

                    // إعادة تحميل تفاصيل المهمة
                    _loadTaskDetails();
                  }
                }
              },
            );
          },
        ),
      PopupMenuButton<String>(
        onSelected: (value) {
          if (value == 'delete') _showDeleteConfirmation();
        },
        itemBuilder: (context) => [
          PopupMenuItem(
            value: 'delete',
            textStyle: const TextStyle(color: AppColors.error),
            child: Text('deleteTask'.tr, style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    ];
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: AppColors.error, size: 64),
            const SizedBox(height: 24),
            Text(
              'حدث خطأ',
              style: TextStyle(color: AppColors.textPrimary),
              
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(error, textAlign: TextAlign.center, style: TextStyle(color: AppColors.textSecondary)),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadTaskDetails,
              icon: const Icon(Icons.refresh),
              label: Text('إعادة المحاولة', style: TextStyle(color: AppColors.white)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLargeScreenLayout(Task task) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: _leftPanelFlex.round(),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: _buildTaskHeader(task),
          ),
        ),
        if (_showSidebar)
          DraggableDivider(
            onDrag: (delta) {
              setState(() {
                final flexDelta = delta /
                    (MediaQuery.of(context).size.width > 1200 ? 80.0 : 40.0);
                _leftPanelFlex = (_leftPanelFlex + flexDelta)
                    .clamp(_minPanelFlex, _maxPanelFlex);
                _rightPanelFlex = (_rightPanelFlex - flexDelta)
                    .clamp(_minPanelFlex, _maxPanelFlex);
              });
            },
          ),
        if (_showSidebar)
          Expanded(
            flex: _rightPanelFlex.round(),
            child: _buildTabs(task),
          ),
      ],
    );
  }

  Widget _buildSmallScreenLayout(Task task) {
    return Column(
      children: [
        _buildTaskHeader(task),
        Expanded(child: _buildTabs(task)),
      ],
    );
  }

  /// بناء تبويب مع عداد
  Widget _buildTabWithBadge(String text, int count) {
    return Tab(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(text, style: TextStyle(color: AppColors.textPrimary)),
          if (count > 0) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: AppStyles.labelSmall.copyWith(
                  color: AppColors.surface,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTabs(Task task) {
    return Column(
      children: [
        GetBuilder<TaskController>(
          id: 'task_details',
          builder: (controller) {
            final currentTask = controller.currentTask ?? task;
            final commentsCount = currentTask.comments.length;
            final attachmentsCount = currentTask.attachments.length;
            final contributorsCount = controller.taskContributors.length;
            final subtasksCount = currentTask.subtasks.length;

            return TabBar(
              controller: _tabController,
              isScrollable: true,
              tabs: [
                const Tab(text: 'النظرة الشاملة'),
                const Tab(text: 'التفاصيل'),
                _buildTabWithBadge('المهام الفرعية', subtasksCount),
                const Tab(text: 'تقدم المهمة'),
                _buildTabWithBadge('الملفات', attachmentsCount),
                const Tab(text: 'المستندات'),
                //تم تضتفه البلدر هذا من اجل تحديث عدد التعليقات تلقائ
                _buildTabWithBadge('التعليقات', commentsCount),
             
                const Tab(text: 'المحادثة'),
                const Tab(text: 'سجل النشاط'),
                const Tab(text: 'تحويلات المهمة'),
                _buildTabWithBadge('المساهمون', contributorsCount),
                const Tab(text: 'تفاصيل المساهمات'),
              ],
            );
          },
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              const TaskOverviewTab(),
              _buildDetailsTab(task),
              SubtasksTab(task: task),
              const TaskProgressTab(),
              const TaskAttachmentsTab(),
              EnhancedTaskDocumentsTab(task: task),
              SimpleTaskCommentsTab(taskId: task.id), // استخدام النسخة المبسطة مباشرة
              _buildConversationTab(),
           
              TaskActivityLogTab(taskId: task.id.toString()),
              TaskTransferHistoryTab(taskId: task.id.toString()),
              ContributorsTab(taskId: task.id.toString()),
              SimpleContributorContributionsTab(
                taskId: task.id.toString(),
                onContributorDeselected: () {
                  Get.find<TaskController>().selectedContributorId = '';
                  _tabController.animateTo(_contributorsTabIndex);
                },
              ),
            ],
          ),
        ),
        if (_tabController.index == _conversationTabIndex) _buildMessageInput(),
      ],
    );
  }
Widget _buildTaskHeader(Task task) {
  return Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: AppColors.card,
      border: Border(bottom: BorderSide(color: AppColors.getBorderColor())),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // استخدام MediaQuery لتحديد عرض الشاشة - تعديل لجعل الصف ريسبونسف
        LayoutBuilder(
          builder: (context, constraints) {
            // إذا كانت الشاشة صغيرة جدًا، نخلي العناصر تصطف عموديًا بدل أفقيًا
            bool isSmallScreen = constraints.maxWidth < 400;
            return isSmallScreen
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان المهمة - يستخدم كامل العرض
                      Text(
                        task.title,
                        style: AppStyles.headingMedium,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    
                      const SizedBox(height: 8),
                      // الحالة وأولوية المهمة في صف أفقي مع إمكانية لف
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          _buildStatusBadge(task),
                          _buildPriorityBadge(task),
                        ],
                      ),
                    ],
                  )
                : Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          task.title,
                          style: AppStyles.headingMedium,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Flexible(fit: FlexFit.loose, child: _buildStatusBadge(task)),
                      const SizedBox(width: 8),
                      Flexible(fit: FlexFit.loose, child: _buildPriorityBadge(task)),
                    ],
                  );
                  
          },
        ),
        const SizedBox(height: 12),
        //رقم المهمة
Row(
children: [
   Icon(Icons.numbers, color: AppColors.primary, size: 20),
  Text("رقم المهمة: ", style: AppStyles.labelMedium.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),),
  const SizedBox(width: 6),
  Text(task.id.toString(), style: AppStyles.labelMedium.copyWith(color: AppColors.textSecondary, fontWeight: FontWeight.bold),),
],
),
        // المسؤول عن المهمة
        if (task.assignee?.name != null && task.assignee!.name.isNotEmpty)
          Row(
            children: [
              Icon(Icons.person, color: AppColors.primary, size: 20),
              const SizedBox(width: 6),
              Text(
                'المسؤول:',
                style: AppStyles.labelMedium.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
              ),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  task.assignee!.name,
                  style: AppStyles.bodySmall.copyWith(color: AppColors.textSecondary,fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis, // تعديل: لإخفاء النص الطويل
                ),
              ),
            ],
          ),
        if (task.assignee?.name != null && task.assignee!.name.isNotEmpty)
          const SizedBox(height: 8),

        // تاريخ الاستحقاق
        Row(
          children: [
            Icon(Icons.event, color: AppColors.primary, size: 20),
            const SizedBox(width: 6),
            Text(
              'تاريخ الاستحقاق:',
              style: AppStyles.labelMedium.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
            ),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                task.dueDate != null
                    ? DateFormatter.formatDate(DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000))
                    : 'غير محدد',
                style: AppStyles.bodySmall.copyWith(color: AppColors.textSecondary,fontWeight: FontWeight.bold),
                overflow: TextOverflow.ellipsis, // تعديل: دعم النص الطويل
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // وصف المهمة
        if (task.description != null && task.description!.trim().isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 4),
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(18),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
               crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.description, color: AppColors.primary, size: 18),
                const SizedBox(width: 8),
Text(
              ' الوصف المختصر:',
              style: AppStyles.labelMedium.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
            ),                Expanded(
                  child: Text(
                    task.description!,
                    style: AppStyles.bodySmall.copyWith(color: AppColors.textPrimary,fontWeight: FontWeight.bold),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

        const SizedBox(height: 5),

        // شريط نسبة التقدم وزر تحديث التقدم - تعديل ليصبح ريسبونسف مع مساحات مرنة
        GetBuilder<TaskController>(
          id: 'task_details',
          builder: (controller) {
            final currentTask = controller.currentTask ?? task;
            return Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: (currentTask.completionPercentage) / 100,
                    backgroundColor: AppColors.progressBackground,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                    minHeight: 8,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 5), // تقليل المسافة لتناسب الشاشات الصغيرة
                SizedBox(
                  width: 40, // تعديل: عرض ثابت للنسبة حتى لا يأخذ مساحة كبيرة
                  child: Text(
                    '${task.completionPercentage.toInt()}%',
                    style: AppStyles.labelSmall.copyWith(
                      color: AppColors.primary,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: AppColors.getShadowColor(0.5),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 8),
                Flexible(
                  // تعديل: زر التحديث يمكن أن يصغر في الشاشات الضيقة
                  child: _permissionService.canUpdateTaskProgress()
                      ? ElevatedButton.icon(
                          icon: Icon(Icons.update, size: 18, color: AppColors.surface),
                          label: Text('تحديث التقدم', style: AppStyles.labelMedium.copyWith(color: AppColors.surface)),
                          style: AppStyles.primaryButtonStyle,
                          onPressed: () => _showUpdateProgressDialog(currentTask),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            );
          },
        ),

        const SizedBox(height: 12),

        // المهام الفرعية كما هي بدون تعديل
        _buildSubtasksInfo(task),
      ],
    ),
  );
}

// تفصيل بناء بادج الحالة لجعل الكود أنظف وإعادة استخدام
Widget _buildStatusBadge(Task task) {
  return ConstrainedBox(
    constraints: const BoxConstraints(maxWidth: 120),
    child: Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.getTaskStatusColor(task.status).withAlpha(40),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(width: 4),
          Icon(
            getStatusIcon(task.status),
            size: 16,
            color: AppColors.getTaskStatusColor(task.status),
          ),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              getStatusText(task.status),
              style: AppStyles.labelSmall.copyWith(
                color: AppColors.getTaskStatusColor(task.status),
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    ),
  );
}

// تفصيل بناء بادج الأولوية لجعل الكود أنظف وإعادة استخدام
Widget _buildPriorityBadge(Task task) {
  return ConstrainedBox(
    constraints: const BoxConstraints(maxWidth: 120),
    child: Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.getTaskPriorityColor(task.priority).withAlpha(40),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(width: 4),
          Icon(TaskDetailHelpers.getPriorityIconByName(task.priority),
              size: 16,
              color: AppColors.getTaskPriorityColor(task.priority)),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              TaskDetailHelpers.getPriorityTextByName(task.priority),
              style: AppStyles.labelSmall.copyWith(
                color: AppColors.getTaskPriorityColor(task.priority),
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    ),
  );
}

  // Widget _buildTaskHeader(Task task) {
  //   return Container(
  //     padding: const EdgeInsets.all(16),
  //     decoration: BoxDecoration(
  //       color: AppColors.card,
  //       border: Border(bottom: BorderSide(color: AppColors.getBorderColor())),
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Row(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Expanded(
  //               child: Text(
  //                 task.title,
  //                 style: AppStyles.headingMedium,
  //                 overflow: TextOverflow.ellipsis,
  //                 maxLines: 1,
  //               ),
  //             ),
  //             const SizedBox(width: 8),
  //             Flexible(
  //               fit: FlexFit.loose,
  //               child: ConstrainedBox(
  //                 constraints: const BoxConstraints(maxWidth: 120),
  //                 child: Container(
  //                   padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
  //                   decoration: BoxDecoration(
  //                     color: AppColors.getTaskStatusColor(task.status)
  //                         .withAlpha(40),
  //                     borderRadius: BorderRadius.circular(16),
  //                   ),
  //                   child: Row(
  //                     mainAxisSize: MainAxisSize.min,
  //                     children: [
  //                       const SizedBox(width: 4),
  //                       Icon(
  //                         getStatusIcon(task.status),
  //                         size: 16,
  //                         color: AppColors.getTaskStatusColor(task.status),
  //                       ),
  //                       const SizedBox(width: 4),
  //                       Flexible(
  //                         child: Text(
  //                           getStatusText(task.status),
  //                           style: TextStyle(
  //                             color: AppColors.getTaskStatusColor(task.status),
  //                             fontSize: 12,
  //                           ),
  //                           overflow: TextOverflow.ellipsis,
  //                           maxLines: 1,
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               ),
  //             ),
  //             const SizedBox(width: 8),
  //             Flexible(
  //               fit: FlexFit.loose,
  //               child: ConstrainedBox(
  //                 constraints: const BoxConstraints(maxWidth: 120),
  //                 child: Container(
  //                   padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
  //                   decoration: BoxDecoration(
  //                     color: AppColors.getTaskPriorityColor(task.priority)
  //                         .withAlpha(40),
  //                     borderRadius: BorderRadius.circular(16),
  //                   ),
  //                   child: Row(
  //                     mainAxisSize: MainAxisSize.min,
  //                     children: [
  //                       const SizedBox(width: 4),
  //                       Icon(TaskDetailHelpers.getPriorityIconByName(task.priority),
  //                           size: 16,
  //                           color: AppColors.getTaskPriorityColor(task.priority)),
  //                       const SizedBox(width: 4),
  //                       Flexible(
  //                         child: Text(
  //                           TaskDetailHelpers.getPriorityTextByName(task.priority),
  //                           style: TextStyle(
  //                             color: AppColors.getTaskPriorityColor(task.priority),
  //                             fontSize: 12,
  //                             fontWeight: FontWeight.bold,
  //                           ),
  //                           overflow: TextOverflow.ellipsis,
  //                           maxLines: 1,
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           ],
  //         ),
  //         const SizedBox(height: 12),
  //         // المسؤول عن المهمة
  //         if (task.assignee?.name != null && task.assignee!.name.isNotEmpty)
  //           Row(
  //             children: [
  //               Icon(Icons.person, color: AppColors.primary, size: 20),
  //               const SizedBox(width: 6),
  //               Text(
  //                 'المسؤول:',
  //                 style: AppStyles.labelMedium.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
  //               ),
  //               const SizedBox(width: 6),
  //               Text(
  //                 task.assignee!.name,
  //                 style: AppStyles.bodySmall.copyWith(color: AppColors.textSecondary),
  //               ),
  //             ],
  //           ),
  //         if (task.assignee?.name != null && task.assignee!.name.isNotEmpty)
  //           const SizedBox(height: 8),
  //         // تاريخ الاستحقاق
  //         Row(
  //           children: [
  //             Icon(Icons.event, color: AppColors.primary, size: 20),
  //             const SizedBox(width: 6),
  //             Text(
  //               'تاريخ الاستحقاق:',
  //               style: AppStyles.labelMedium.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
  //             ),
  //             const SizedBox(width: 6),
  //             Text(
  //               task.dueDate != null
  //                   ? DateFormatter.formatDate(DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000))
  //                   : 'غير محدد',
  //               style: AppStyles.bodySmall.copyWith(color: AppColors.textSecondary),
  //             ),
  //           ],
  //         ),
  //         const SizedBox(height: 8),
  //         // وصف المهمة
  //         if (task.description != null && task.description!.trim().isNotEmpty)
  //           Container(
  //             margin: const EdgeInsets.only(top: 4),
  //             padding: const EdgeInsets.all(10),
  //             decoration: BoxDecoration(
  //               color: AppColors.primary.withAlpha(18),
  //               borderRadius: BorderRadius.circular(10),
  //             ),
  //             child: Row(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Icon(Icons.description, color: AppColors.primary, size: 18),
  //                 const SizedBox(width: 8),
  //                 Expanded(
  //                   child: Text(
  //                     task.description!,
  //                     style: AppStyles.bodySmall.copyWith(color: AppColors.textPrimary),
  //                     maxLines: 3,
  //                     overflow: TextOverflow.ellipsis,
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),
  //         // شريط نسبة التقدم وزر تحديث التقدم
  //         const SizedBox(height: 12),
  //         GetBuilder<TaskController>(
  //           id: 'task_details',
  //           builder: (controller) {
  //             final currentTask = controller.currentTask ?? task;
  //             return Row(
  //               children: [
  //                 Expanded(

  //                   child: LinearProgressIndicator(
  //                     value: (currentTask.completionPercentage) / 100,
  //                     backgroundColor: AppColors.progressBackground,
  //                     valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
  //                     minHeight: 8,
  //                     borderRadius: BorderRadius.circular(4),
                      
  //                   ),
  //                 ),
  //                  Text(
                    
  //                 '${task.completionPercentage.toInt()}%',
  //                 style: TextStyle(
  //                   color: AppColors.white,
  //                   fontSize: 10,
  //                   fontWeight: FontWeight.bold,
  //                   shadows: [
  //                     Shadow(
  //                       color: AppColors.getShadowColor(0.5),
  //                       blurRadius: 2,
  //                       offset: const Offset(0, 1),
  //                     ),
  //                   ],
  //                 ),
  //               ),
  //                 const SizedBox(width: 12),
  //                 ElevatedButton.icon(
  //                   icon: const Icon(Icons.update, size: 18,color: AppColors.white,),
  //                   label: const Text('تحديث التقدم'),
  //                   style: ElevatedButton.styleFrom(
  //                     backgroundColor: AppColors.primary,
  //                     foregroundColor: AppColors.white,
  //                     padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
  //                     textStyle: const TextStyle(fontSize: 13, fontWeight: FontWeight.bold),
  //                   ),
  //                   onPressed: () => _showUpdateProgressDialog(currentTask),
  //                 ),
  //               ],
  //             );
  //           },
  //         ),
  //         const SizedBox(height: 12),
  //         // المهام الفرعية
  //         _buildSubtasksInfo(task),
  //       ],
  //     ),
  //   );
  // }

  Future<void> _updateTaskProgress(Task task, int progressValue, String notes) async {
    final userId = Get.find<AuthController>().currentUser.value?.id;
    if (userId == null) {
      Get.snackbar(
        'خطأ',
        'لم يتم العثور على معرف المستخدم',
        backgroundColor: AppColors.error.withAlpha(51),
        colorText: AppColors.error,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    try {
      final taskController = Get.find<TaskController>();
      final originalTask = taskController.currentTask;

      // تحديث القيمة محلياً أولاً لتحديث فوري للواجهة
      if (originalTask != null) {
        // إنشاء نسخة محدثة من المهمة
        final updatedTask = originalTask.copyWith(
          completionPercentage: progressValue,
        );

        // تحديث المهمة في المتحكم
        taskController.updateCurrentTask(updatedTask);

        // إجبار تحديث الواجهة فوراً
        taskController.update(['task_details']);
        setState(() {});
      }

      final success = await taskController.updateTaskProgress(
        task.id,
        userId,
        progressValue.toDouble(),
        notes: notes,
      );

      if (success) {
        // استخدام التحديث الشامل الجديد
        await taskController.refreshTaskDetails(task.id);

        Get.snackbar(
          'نجح',
          'تم تحديث تقدم المهمة بنجاح',
          backgroundColor: AppColors.success.withAlpha(51),
          colorText: AppColors.success,
          duration: const Duration(seconds: 3),
        );
      } else {
        // في حالة الفشل، استرجع المهمة الأصلية
        if (originalTask != null) {
          taskController.updateCurrentTask(originalTask);
          taskController.update(['task_details']);
          setState(() {});
        }

        Get.snackbar(
          'خطأ',
          'فشل في تحديث تقدم المهمة',
          backgroundColor: AppColors.error.withAlpha(51),
          colorText: AppColors.error,
          duration: const Duration(seconds: 3),
        );
      }
    } catch (e) {
      // في حالة الخطأ، استرجع المهمة الأصلية
      final taskController = Get.find<TaskController>();
      final originalTask = task; // استخدام المهمة الأصلية المرسلة للدالة

      taskController.updateCurrentTask(originalTask);
      taskController.update(['task_details']);
      setState(() {});

      Get.snackbar(
        'خطأ',
        'حدث خطأ: ${e.toString()}',
        backgroundColor: AppColors.error.withAlpha(51),
        colorText: AppColors.error,
        duration: const Duration(seconds: 3),
      );
    }
  }

  void _showUpdateProgressDialog(Task task) {
    final TextEditingController progressController = TextEditingController(text: (task.completionPercentage).toString());
    final TextEditingController notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تحديث نسبة التقدم', style: TextStyle(color: AppColors.textPrimary)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: progressController,
                style: TextStyle(color: AppColors.textPrimary),
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'نسبة التقدم (%)',
                  labelStyle: TextStyle(color: AppColors.textSecondary),
                  helperText: 'أدخل رقم بين 0 و 100',
                  helperStyle: TextStyle(color: AppColors.textSecondary),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.border),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.border),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.primary, width: 2.0),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: notesController,
                style: TextStyle(color: AppColors.textPrimary),
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  labelStyle: TextStyle(color: AppColors.textSecondary),
                  hintText: 'أضف ملاحظات حول التقدم...',
                  hintStyle: TextStyle(color: AppColors.textSecondary),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.border),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.border),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.primary, width: 2.0),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('إلغاء', style: TextStyle(color: AppColors.textPrimary)),
            ),
            ElevatedButton(
              onPressed: () async {
                final value = int.tryParse(progressController.text);
                if (value != null && value >= 0 && value <= 100) {
                  // إغلاق dialog الإدخال أولاً
                  Navigator.of(context).pop();

                  // تنفيذ تحديث التقدم
                  await _updateTaskProgress(task, value, notesController.text.trim());
                } else {
                  Get.snackbar(
                    'خطأ',
                    'يرجى إدخال رقم صحيح بين 0 و 100',
                    backgroundColor: AppColors.warning.withAlpha(51),
                    colorText: AppColors.warning,
                    duration: const Duration(seconds: 3),
                  );
                }
              },
              child: Text('حفظ', style: TextStyle(color: AppColors.white)),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSubtasksInfo(Task task) {
    return GetBuilder<TaskController>(
      id: 'task_details',
      builder: (controller) {
        final currentTask = controller.currentTask ?? task;
        final subtasksCount = currentTask.subtasks.length;
        final completedCount = currentTask.subtasks.where((s) => s.isCompleted).length;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Wrap(
              spacing: 8,
              runSpacing: 8,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                const Icon(Icons.checklist, size: 16, color: AppColors.primary),
                const SizedBox(width: 8),
                Text('المهام الفرعية',
                    style: AppStyles.labelMedium
                        .copyWith(color: AppColors.primary)),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(50),
                      borderRadius: BorderRadius.circular(12)),
                  child: Text('$completedCount/$subtasksCount مكتملة',
                      style: AppStyles.bodySmall.copyWith(
                          color: AppColors.textPrimary, fontWeight: FontWeight.bold)),
                ),
                TextButton(
                  onPressed: () => _tabController.animateTo(_subtasksTabIndex),
                  child: Text('عرض الكل',
                      style:
                          AppStyles.bodySmall.copyWith(color: AppColors.primary)),
                ),
              ],
            ),
            const SizedBox(height: 4),
            LinearProgressIndicator(
              value: subtasksCount > 0 ? completedCount / subtasksCount : 0,
              backgroundColor: AppColors.progressBackground,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              minHeight: 6,
              borderRadius: BorderRadius.circular(3),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDetailsTab(Task task) {
    return GetBuilder<TaskController>(
      id: 'task_details',
      builder: (controller) {
        final currentTask = controller.currentTask ?? task;
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // قسم الوصف
              _buildDescriptionSection(currentTask),
              const SizedBox(height: 24),

              // قسم المعلومات الأساسية
              _buildBasicInfoSection(currentTask),
              const SizedBox(height: 24),

              // قسم التواريخ والمواعيد
              _buildDatesSection(currentTask),
              const SizedBox(height: 24),

              // قسم الأشخاص والمسؤوليات
              _buildPeopleSection(currentTask),
              const SizedBox(height: 24),

              // قسم التقدم والوقت
              _buildProgressSection(currentTask),
              const SizedBox(height: 24),

              // قسم الإحصائيات
              _buildStatisticsSection(currentTask),
            ],
          ),
        );
      },
    );
  }

  /// بناء قسم الوصف
  Widget _buildDescriptionSection(Task task) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.description, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'وصف المهمة',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.primary.withAlpha(51)),
              ),
              child: Text(
                task.description?.isNotEmpty == true
                    ? task.description!
                    : 'لا يوجد وصف للمهمة.',
                style: AppStyles.bodyMedium.copyWith(
                  color: task.description?.isNotEmpty == true
                      ? AppColors.textPrimary
                      : AppColors.textSecondary,
                  fontStyle: task.description?.isNotEmpty == true
                      ? FontStyle.normal
                      : FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم المعلومات الأساسية
  Widget _buildBasicInfoSection(Task task) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'المعلومات الأساسية',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // شبكة المعلومات الأساسية
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 3.2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                // رقم المهمة
                _buildInfoCard(
                  'رقم المهمة',
                  '${task.id}',
                  Icons.tag,
                  AppColors.primary.withAlpha(25),
                  AppColors.primary,
                ),

                // نوع المهمة
                _buildInfoCard(
                  'النوع',
                  task.taskType?.name ?? 'غير محدد',
                  Icons.category,
                  AppColors.info.withAlpha(25),
                  AppColors.info,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // الحالة والأولوية في صف منفصل
            Row(
              children: [
                // بطاقة الحالة
                Expanded(
                  child: _buildStatusCard(task),
                ),
                const SizedBox(width: 12),
                // بطاقة الأولوية
                Expanded(
                  child: _buildPriorityCard(task),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات صغيرة
  Widget _buildInfoCard(String title, String value, IconData icon, Color bgColor, Color iconColor) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: iconColor.withAlpha(51)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: iconColor),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppStyles.bodyMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
              fontSize: 13,
            ),
            textAlign: TextAlign.start,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الحالة
  Widget _buildStatusCard(Task task) {
    final statusColor = AppColors.getTaskStatusColor(task.status);
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            statusColor.withAlpha(25),
            statusColor.withAlpha(51),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withAlpha(102)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(getStatusIcon(task.status), size: 18, color: statusColor),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  'الحالة',
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              getStatusText(task.status),
              style: AppStyles.bodySmall.copyWith(
                color: AppColors.surface,
                fontWeight: FontWeight.bold,
                fontSize: 11,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الأولوية
  Widget _buildPriorityCard(Task task) {
    final priorityColor = AppColors.getTaskPriorityColor(task.priority);
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            priorityColor.withAlpha(25),
            priorityColor.withAlpha(51),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: priorityColor.withAlpha(102)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(TaskDetailHelpers.getPriorityIconByName(task.priority), size: 18, color: priorityColor),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  'الأولوية',
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                decoration: BoxDecoration(
                  color: priorityColor,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  TaskDetailHelpers.getPriorityTextByName(task.priority),
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 11,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Spacer(),
              // أزرار تغيير الأولوية
              _buildPriorityButtons(task),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value, IconData icon, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Icon(icon, size: 16, color: AppColors.primary),
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: AppStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                fontSize: 13,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: AppStyles.bodyMedium.copyWith(
                color: valueColor ?? AppColors.textPrimary,
                fontWeight: valueColor != null ? FontWeight.bold : FontWeight.normal,
                fontSize: 13,
              ),
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم التواريخ والمواعيد
  Widget _buildDatesSection(Task task) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'التواريخ والمواعيد',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              'تاريخ الإنشاء',
              DateFormatter.formatDateTime(DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000)),
              Icons.add_circle_outline,
            ),
            if (task.startDate != null)
              _buildInfoRow(
                'تاريخ البداية',
                DateFormatter.formatDateTime(DateTime.fromMillisecondsSinceEpoch(task.startDate! * 1000)),
                Icons.play_circle_outline,
              ),
            _buildInfoRow(
              'تاريخ الاستحقاق',
              task.dueDate != null
                  ? DateFormatter.formatDateTime(DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000))
                  : 'غير محدد',
              Icons.event,
              valueColor: task.dueDate != null &&
                         DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000).isBefore(DateTime.now()) &&
                         task.status != 'completed'
                  ? AppColors.statusCancelled
                  : null,
            ),
            if (task.completedAt != null)
              _buildInfoRow(
                'تاريخ الإنجاز',
                DateFormatter.formatDateTime(DateTime.fromMillisecondsSinceEpoch(task.completedAt! * 1000)),
                Icons.check_circle,
                valueColor: AppColors.statusCompleted,
              ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الأشخاص والمسؤوليات
  Widget _buildPeopleSection(Task task) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.people, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'الأشخاص والمسؤوليات',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              'منشئ المهمة',
              task.creator?.name ?? 'غير محدد',
              Icons.person_add,
            ),
            _buildInfoRow(
              'المسؤول عن المهمة',
              task.assignee?.name ?? 'غير مُعيَّن',
              Icons.person,
              valueColor: task.assignee != null ? AppColors.primary : AppColors.statusPending,
            ),
            _buildInfoRow(
              'القسم',
              task.department?.name ?? 'غير محدد',
              Icons.business,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم التقدم والوقت
  Widget _buildProgressSection(Task task) {
    return GetBuilder<TaskController>(
      id: 'task_details',
      builder: (controller) {
        final currentTask = controller.currentTask ?? task;
        return Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.trending_up, color: AppColors.primary, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'التقدم والوقت',
                      style: AppStyles.titleMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // شريط التقدم
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'نسبة الإنجاز',
                            style: AppStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600),
                          ),
                          Text(
                            '${currentTask.completionPercentage}%',
                            style: AppStyles.titleSmall.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: currentTask.completionPercentage / 100,
                        backgroundColor: AppColors.progressBackground,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                        minHeight: 8,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // معلومات الوقت
                if (currentTask.estimatedTime != null)
                  _buildInfoRow(
                    'الوقت المقدر',
                    '${currentTask.estimatedTime} دقيقة',
                    Icons.timer,
                  ),
                if (currentTask.actualTime != null)
                  _buildInfoRow(
                    'الوقت الفعلي',
                    '${currentTask.actualTime} دقيقة',
                    Icons.timer_outlined,
                    valueColor: currentTask.actualTime! > (currentTask.estimatedTime ?? 0)
                        ? AppColors.statusPending
                        : AppColors.statusCompleted,
                  ),
                if (currentTask.estimatedTime != null && currentTask.actualTime != null)
                  _buildInfoRow(
                    'الفرق في الوقت',
                    '${(currentTask.actualTime! - currentTask.estimatedTime!).abs()} دقيقة ${currentTask.actualTime! > currentTask.estimatedTime! ? 'زيادة' : 'توفير'}',
                    currentTask.actualTime! > currentTask.estimatedTime! ? Icons.trending_up : Icons.trending_down,
                    valueColor: currentTask.actualTime! > currentTask.estimatedTime! ? AppColors.statusCancelled : AppColors.statusCompleted,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatisticsSection(Task task) {
    return GetBuilder<TaskController>(
      id: 'task_details', // إضافة معرف للتحديث التلقائي
      builder: (controller) {
        // استخدام البيانات المحدثة من الكونترولر
        final currentTask = controller.currentTask ?? task;
        final subtasksCount = currentTask.subtasks.length;
        final completedSubtasks = currentTask.subtasks.where((s) => s.isCompleted).length;
        final commentsCount = currentTask.comments.length;
        final attachmentsCount = currentTask.attachments.length;

        return Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.analytics, color: AppColors.primary, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'الإحصائيات',
                      style: AppStyles.titleMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // شبكة الإحصائيات
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  childAspectRatio: 3.0,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  children: [
                    _buildStatCard(
                      'المهام الفرعية',
                      '$completedSubtasks/$subtasksCount',
                      Icons.checklist,
                      subtasksCount > 0 ? completedSubtasks / subtasksCount : 0,
                    ),
                    _buildStatCard(
                      'التعليقات',
                      '$commentsCount',
                      Icons.comment,
                      null,
                    ),
                    _buildStatCard(
                      'المرفقات',
                      '$attachmentsCount',
                      Icons.attach_file,
                      null,
                    ),
                    _buildStatCard(
                      'الأيام المتبقية',
                      currentTask.dueDate != null
                          ? '${DateTime.fromMillisecondsSinceEpoch(currentTask.dueDate! * 1000).difference(DateTime.now()).inDays}'
                      : 'غير محدد',
                  Icons.calendar_today,
                  null,
                  valueColor: currentTask.dueDate != null &&
                             DateTime.fromMillisecondsSinceEpoch(currentTask.dueDate! * 1000).isBefore(DateTime.now())
                      ? AppColors.statusCancelled
                      : null,
                ),
              ],
            ),
          ],
        ),
        ),
            );
      },
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, double? progress, {Color? valueColor}) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: AppColors.primary.withAlpha(25),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primary.withAlpha(51)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(icon, size: 14, color: AppColors.primary),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  title,
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
          const SizedBox(height: 3),
          Text(
            value,
            style: AppStyles.titleSmall.copyWith(
              color: valueColor ?? AppColors.textPrimary,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          if (progress != null) ...[
            const SizedBox(height: 3),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: AppColors.progressBackground,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              minHeight: 2,
              borderRadius: BorderRadius.circular(1),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildConversationTab() {
    return GetBuilder<TaskMessagesController>(
      init: TaskMessagesController(),
      builder: (messagesController) {
        final taskIdInt = int.parse(widget.taskId);

        return Column(
          children: [
            // شريط الرسائل المثبتة
            Obx(() {
              if (messagesController.pinnedMessages.isNotEmpty) {
                return Container(
                  height: 60,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: AppColors.statusPending.withAlpha(26),
                    border: Border(bottom: BorderSide(color: AppColors.statusPending.withAlpha(77))),
                  ),
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: messagesController.pinnedMessages.length,
                    itemBuilder: (context, index) {
                      final pinnedMessage = messagesController.pinnedMessages[index];
                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: AppColors.statusPending.withAlpha(51),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: AppColors.statusPending.withAlpha(102)),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.push_pin, size: 14, color: AppColors.statusPending),
                            const SizedBox(width: 3),
                            Flexible(
                              child: Text(
                                pinnedMessage.content.length > 25
                                    ? '${pinnedMessage.content.substring(0, 25)}...'
                                    : pinnedMessage.content,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: AppColors.statusPending,
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

            // قائمة الرسائل مع تحديث فوري
            Expanded(
              child: GetBuilder<TaskMessagesController>(
                builder: (controller) => Obx(() {
                  // إعادة تحميل الرسائل عند التحديث
                  final currentMessages = controller.getTaskMessages(taskIdInt);

                  if (controller.isLoading && currentMessages.isEmpty) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (currentMessages.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.chat_bubble_outline, size: 64, color: AppColors.textSecondary),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد رسائل بعد',
                            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'ابدأ محادثة جديدة!',
                            style: TextStyle(fontSize: 14, color: AppColors.textSecondary),
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () => controller.loadTaskMessages(taskIdInt),
                    child: ListView.builder(
                      controller: _messageScrollController,
                      padding: const EdgeInsets.all(8.0),
                      itemCount: currentMessages.length,
                      reverse: true,
                      itemBuilder: (context, index) {
                        final message = currentMessages[index];
                        final bool isCurrentUser = message.senderId ==
                            Get.find<AuthController>().currentUser.value?.id;

                        return _buildMessageBubble(message, isCurrentUser, controller);
                      },
                    ),
                  );
            
                }),
              ),
            ),

            // مؤشر الكتابة
            Obx(() {
              final typingUsers = messagesController.getTypingUsers(taskIdInt);
              if (typingUsers.isNotEmpty) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    typingUsers.length == 1
                        ? '${typingUsers.first} يكتب...'
                        : '${typingUsers.length} أشخاص يكتبون...',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        );
      },
    );
  }

  /// بناء فقاعة الرسالة
  Widget _buildMessageBubble(
    TaskMessageResponse message,
    bool isCurrentUser,
    TaskMessagesController messagesController,
  ) {
    return GestureDetector(
      onLongPress: () => _showMessageOptions(message, messagesController),
      child: Align(
        alignment: isCurrentUser ? Alignment.centerRight : Alignment.centerLeft,
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          padding: const EdgeInsets.all(12.0),
          decoration: BoxDecoration(
            color: isCurrentUser
                ? AppColors.primary.withAlpha(51)
                : AppColors.background,
            borderRadius: BorderRadius.circular(12.0),
            border: message.isPinned
                ? Border.all(color: AppColors.statusPending, width: 2)
                : null,
          ),
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.7,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الرسالة
              Row(
                children: [
                  Expanded(
                    child: Text(
                      message.senderName,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isCurrentUser ? AppColors.primary : AppColors.textPrimary,
                        fontSize: 13,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 4),
                  if (message.isPinned)
                    Icon(Icons.push_pin, size: 14, color: AppColors.statusPending),
                  if (message.priority > 0)
                    Icon(
                      message.priority == 1 ? Icons.priority_high : Icons.warning,
                      size: 14,
                      color: message.priority == 1 ? AppColors.statusPending : AppColors.statusCancelled,
                    ),
                ],
              ),

              const SizedBox(height: 4.0),

              // الرد على رسالة
              if (message.replyToMessage != null)
                Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.background,
                    borderRadius: BorderRadius.circular(8),
                    border: Border(
                      left: BorderSide(color: AppColors.primary, width: 3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        message.replyToMessage!.senderName,
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        message.replyToMessage!.content.length > 50
                            ? '${message.replyToMessage!.content.substring(0, 50)}...'
                            : message.replyToMessage!.content,
                        style: TextStyle(fontSize: 11, color: AppColors.textSecondary),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ],
                  ),
                ),

              // محتوى الرسالة
              Text(message.content, style: TextStyle(color: AppColors.textPrimary)),

              const SizedBox(height: 4.0),

              // تذييل الرسالة
              Wrap(
                alignment: WrapAlignment.spaceBetween,
                children: [
                  Text(
                    DateFormatter.formatDateTime(message.createdAtDateTime),
                    style: TextStyle(fontSize: 10, color: AppColors.textSecondary),
                  ),
                  if (message.isEdited)
                    Text(
                      'معدلة',
                      style: TextStyle(
                        fontSize: 10,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  if (isCurrentUser && message.totalRecipients > 1)
                    Text(
                      '${message.readByCount}/${message.totalRecipients}',
                      style: TextStyle(
                        fontSize: 10,
                        color: message.readByCount == message.totalRecipients
                            ? AppColors.statusCompleted
                            : AppColors.textSecondary,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
         ),
      );
  }

  /// عرض خيارات الرسالة عند الضغط المطول
  void _showMessageOptions(TaskMessageResponse message, TaskMessagesController messagesController) {
    final currentUserId = Get.find<AuthController>().currentUser.value?.id;
    final isOwner = message.senderId == currentUserId;

    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.reply),
              title: Text('رد على الرسالة', style: TextStyle(color: AppColors.textPrimary)),
              onTap: () {
                Get.back();
                messagesController.setReplyToMessage(message);
              },
            ),
            ListTile(
              leading: Icon(
                message.isPinned ? Icons.push_pin_outlined : Icons.push_pin,
                color: message.isPinned ? AppColors.statusPending : null,
              ),
              title: Text(message.isPinned ? 'إلغاء التثبيت' : 'تثبيت الرسالة', style: TextStyle(color: AppColors.textPrimary)),
              onTap: () {
                Get.back();
                messagesController.pinMessage(message.id, !message.isPinned);
              },
            ),
            if (isOwner) ...[
              ListTile(
                leading: const Icon(Icons.edit),
                title: Text('تعديل الرسالة', style: TextStyle(color: AppColors.textPrimary)),
                onTap: () {
                  Get.back();
                  _showEditMessageDialog(message, messagesController);
                },
              ),
              ListTile(
                leading: Icon(Icons.delete, color: AppColors.statusCancelled),
                title: Text('حذف الرسالة', style: TextStyle(color: AppColors.statusCancelled)),
                onTap: () {
                  Get.back();
                  _showDeleteMessageConfirmation(message, messagesController);
                },
              ),
            ],
            ListTile(
              leading: const Icon(Icons.flag),
              title: Text('تحديد للمتابعة', style: TextStyle(color: AppColors.textPrimary)),
              onTap: () {
                Get.back();
                messagesController.markForFollowUp(message.id, !message.isMarkedForFollowUp);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// عرض حوار تعديل الرسالة
  void _showEditMessageDialog(TaskMessageResponse message, TaskMessagesController messagesController) {
    final controller = TextEditingController(text: message.content);

    Get.dialog(
      AlertDialog(
        title: Text('تعديل الرسالة', style: TextStyle(color: AppColors.textPrimary)),
        content: TextField(
          controller: controller,
          style: TextStyle(color: AppColors.textPrimary),
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'محتوى الرسالة...',
            hintStyle: TextStyle(color: AppColors.textSecondary),
            border: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.border),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.primary, width: 2.0),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء', style: TextStyle(color: AppColors.textPrimary)),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isNotEmpty && controller.text != message.content) {
                messagesController.updateMessage(message.id, controller.text);
              }
              Get.back();
            },
            child: Text('حفظ', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }

  /// عرض تأكيد حذف الرسالة
  void _showDeleteMessageConfirmation(TaskMessageResponse message, TaskMessagesController messagesController) {
    Get.dialog(
      AlertDialog(
        title: Text('حذف الرسالة', style: TextStyle(color: AppColors.textPrimary)),
        content: Text('هل أنت متأكد من حذف هذه الرسالة؟', style: TextStyle(color: AppColors.textSecondary)),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء', style: TextStyle(color: AppColors.textPrimary)),
          ),
          ElevatedButton(
            onPressed: () {
              messagesController.deleteMessage(message.id);
              Get.back();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.statusCancelled),
            child: Text('حذف', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }




  Widget _buildMessageInput() {
    return GetBuilder<TaskMessagesController>(
      builder: (messagesController) {
        return Column(
          children: [
            // عرض الرسالة المحددة للرد
            Obx(() {
              final replyToMessage = messagesController.replyToMessage;
              if (replyToMessage != null) {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.background,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                    border: Border(
                      left: BorderSide(color: AppColors.primary, width: 3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.reply, color: AppColors.primary, size: 18),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'رد على ${replyToMessage.senderName}',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              replyToMessage.content.length > 50
                                  ? '${replyToMessage.content.substring(0, 50)}...'
                                  : replyToMessage.content,
                              style: TextStyle(fontSize: 11, color: AppColors.textSecondary),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 18),
                        onPressed: () => messagesController.clearReplyToMessage(),
                        color: AppColors.textSecondary,
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

            // حقل إدخال الرسالة
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      style: TextStyle(color: AppColors.textPrimary),
                      decoration: InputDecoration(
                        hintText: 'اكتب رسالة...',
                        hintStyle: TextStyle(color: AppColors.textSecondary),
                        border: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.border),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.border),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.primary, width: 2.0),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      textInputAction: TextInputAction.send,
                      onSubmitted: (_) => _sendMessage(),
                      onChanged: (text) => _handleTypingNotification(text),
                      maxLines: null,
                      minLines: 1,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Obx(() {
                    return _permissionService.canSendMessages()
                        ? IconButton(
                            icon: messagesController.isSending
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Icon(Icons.send),
                            color: AppColors.primary,
                            onPressed: messagesController.isSending ? null : _sendMessage,
                          )
                        : const SizedBox.shrink();
                  }),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  void _sendMessage() async {
    if (_messageController.text.isNotEmpty) {
      final messagesController = Get.find<TaskMessagesController>();
      final taskIdInt = int.parse(widget.taskId);

      // إرسال الرسالة باستخدام النظام الجديد
      final success = await messagesController.sendMessage(
        taskId: taskIdInt,
        content: _messageController.text,
        replyToMessageId: messagesController.replyToMessage?.id,
      );

      if (success) {
        _messageController.clear();

        // التمرير لأسفل بعد إرسال الرسالة
        // ملاحظة: محادثات المهام تستخدم reverse: true، لذا نحتاج للتمرير إلى 0
        Future.delayed(const Duration(milliseconds: 100), () {
          if (_messageScrollController.hasClients) {
            _messageScrollController.animateTo(
              0.0, // التمرير إلى أعلى القائمة لأن reverse: true
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في إرسال الرسالة: ${messagesController.error}',
          backgroundColor: AppColors.statusCancelled.withAlpha(26),
          colorText: AppColors.statusCancelled,
        );
      }
    }
  }

  void _showDeleteConfirmation() {
    final taskController = Get.find<TaskController>();
    final task = taskController.currentTask;
    if (task == null) return;

    Get.dialog(
      AlertDialog(
        title: Text('deleteTask'.tr, style: TextStyle(color: AppColors.textPrimary)),
        content: Text('deleteTaskConfirmation'.tr, style: TextStyle(color: AppColors.textSecondary)),
        actions: [
          TextButton(onPressed: () => Get.back(), child: Text('cancel'.tr, style: TextStyle(color: AppColors.textPrimary))),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              await taskController.deleteTask(task.id);
              Get.back(); // Go back from detail screen
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child:
                Text('delete'.tr, style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار تغيير الأولوية
  Widget _buildPriorityButtons(Task task) {
    final permissionService = Get.find<UnifiedPermissionService>();

    // التحقق من صلاحية تغيير الأولوية
    if (!permissionService.canChangeTaskPriority()) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // زر تقليل الأولوية
        if (_permissionService.canChangeTaskPriority())
          IconButton(
            icon: const Icon(Icons.keyboard_arrow_down, size: 18),
            onPressed: () => _changePriority(task, false),
            tooltip: 'تقليل الأولوية',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
        // زر زيادة الأولوية
        if (_permissionService.canChangeTaskPriority())
          IconButton(
            icon: const Icon(Icons.keyboard_arrow_up, size: 18),
            onPressed: () => _changePriority(task, true),
            tooltip: 'زيادة الأولوية',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
      ],
    );
  }

  /// تغيير أولوية المهمة
  Future<void> _changePriority(Task task, bool increase) async {
    final taskController = Get.find<TaskController>();
    final authController = Get.find<AuthController>();

    // تحديد الأولوية الجديدة
    String newPriority;
    switch (task.priority) {
      case 'low':
        newPriority = increase ? 'medium' : 'low';
        break;
      case 'medium':
        newPriority = increase ? 'high' : 'low';
        break;
      case 'high':
        newPriority = increase ? 'urgent' : 'medium';
        break;
      case 'urgent':
        newPriority = increase ? 'urgent' : 'high';
        break;
      default:
        newPriority = 'medium';
    }

    // تجنب التغيير إذا كانت الأولوية نفسها
    if (newPriority == task.priority) {
      return;
    }

    try {
      // تحديث الأولوية
      final success = await taskController.updateTaskPriority(
        task.id,
        authController.currentUser.value!.id,
        newPriority,
      );

      if (success) {
        Get.snackbar(
          'تم التحديث',
          'تم تغيير أولوية المهمة بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.statusCompleted.withAlpha(26),
          colorText: AppColors.statusCompleted,
        );

        // إعادة تحميل تفاصيل المهمة
        await taskController.loadTaskDetails(widget.taskId);
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في تغيير أولوية المهمة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.statusCancelled.withAlpha(26),
          colorText: AppColors.statusCancelled,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تغيير الأولوية: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.statusCancelled.withAlpha(26),
        colorText: AppColors.statusCancelled,
      );
    }
  }

  /// عرض تفاصيل حالة الاتصال
  void _showConnectionDetails(UnifiedSignalRService signalRService) {
    Get.dialog(
      AlertDialog(
        title: Text('حالة اتصال SignalR', style: TextStyle(color: AppColors.textPrimary)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildConnectionStatus('Chat Hub', signalRService.isChatHubConnected),
            _buildConnectionStatus('Task Hub', signalRService.isTaskHubConnected),
            _buildConnectionStatus('Comments Hub', signalRService.isTaskCommentsHubConnected),
            _buildConnectionStatus('Notification Hub', signalRService.isNotificationHubConnected),
            const SizedBox(height: 16),
            Text('URLs:', style: TextStyle(fontWeight: FontWeight.bold, color: AppColors.textPrimary)),
            Text('Chat: ${ApiConfig.baseUrl}/chatHub', style: TextStyle(fontSize: 12, color: AppColors.textSecondary)),
            Text('Task: ${ApiConfig.baseUrl}/taskHub', style: TextStyle(fontSize: 12, color: AppColors.textSecondary)),
            Text('Comments: ${ApiConfig.baseUrl}/taskCommentsHub', style: TextStyle(fontSize: 12, color: AppColors.textSecondary)),
            Text('Notifications: ${ApiConfig.baseUrl}/notificationHub', style: TextStyle(fontSize: 12, color: AppColors.textSecondary)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () async {
              Get.back();
              // محاولة إعادة الاتصال
              await signalRService.reconnectAll();
              Get.snackbar('إعادة الاتصال', 'تم محاولة إعادة الاتصال بجميع الخدمات');
            },
            child: Text('إعادة الاتصال', style: TextStyle(color: AppColors.white)),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إغلاق', style: TextStyle(color: AppColors.textPrimary)),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر حالة الاتصال
  Widget _buildConnectionStatus(String name, bool isConnected) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: isConnected ? AppColors.statusCompleted : AppColors.statusCancelled,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text('$name: ${isConnected ? 'متصل' : 'غير متصل'}', style: TextStyle(color: AppColors.textPrimary)),
        ],
      ),
    );
  }

  /// إنشاء التقرير الشامل للمهمة (كامل)
  Future<void> _generateComprehensiveTaskReport(Task task) async {
    try {
      debugPrint('🚀 بدء إنشاء التقرير الشامل للمهمة ${task.id}...');

      // إنشاء خدمة التقرير الشامل
      final reportService = SimpleComprehensiveTaskReportService();

      // إنشاء التقرير الشامل
      await reportService.generateComprehensiveReport(task.id);

    } catch (e, stackTrace) {
      debugPrint('❌ خطأ في إنشاء التقرير الشامل: $e');
      debugPrint('Stack trace: $stackTrace');

      // إظهار رسالة خطأ للمستخدم
      Get.snackbar(
        'خطأ',
        'فشل في إنشاء التقرير الشامل: ${e.toString()}',
        backgroundColor: AppColors.statusCancelled.withAlpha(26),
        colorText: AppColors.statusCancelled,
        icon: Icon(Icons.error, color: AppColors.statusCancelled),
        duration: const Duration(seconds: 5),
      );
    }
  }

  /// إنشاء التقرير الشامل للمهمة مع فلتر
  Future<void> _generateFilteredComprehensiveTaskReport(Task task) async {
    try {
      debugPrint('🎯 بدء إنشاء التقرير الشامل مع فلتر للمهمة ${task.id}...');

      // إنشاء التقرير مع فلتر
      await SimpleComprehensiveTaskReportService.generateAndShowReportWithFilter(
        context,
        task.id
      );

    } catch (e, stackTrace) {
      debugPrint('❌ خطأ في إنشاء التقرير الشامل مع فلتر: $e');
      debugPrint('Stack trace: $stackTrace');

      // إظهار رسالة خطأ للمستخدم
      Get.snackbar(
        'خطأ',
        'فشل في إنشاء التقرير الشامل مع فلتر: ${e.toString()}',
        backgroundColor: AppColors.statusCancelled.withAlpha(26),
        colorText: AppColors.statusCancelled,
        icon: Icon(Icons.error, color: AppColors.statusCancelled),
        duration: const Duration(seconds: 5),
      );
    }
  }

}
