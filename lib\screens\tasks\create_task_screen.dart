import 'dart:io' if (dart.library.html) 'package:flutter_application_2/utils/web_file_stub.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/task_status_enum.dart';
import 'package:flutter_application_2/repositories/department_repository.dart';
import 'package:flutter_application_2/repositories/user_repository.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart'; 
// import '../../models/task_model.dart'; 
import '../../models/user_model.dart';
import '../../models/department_model.dart';
import '../../models/task_type_models.dart' hide TaskStatus; // Hide TaskStatus from this import
import '../../controllers/auth_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/task_type_controller.dart';
import '../../services/unified_permission_service.dart';
import '../widgets/user_selection_dialog.dart';




class CreateTaskScreen extends StatefulWidget {
  final TaskStatus? initialStatus;

  const CreateTaskScreen({super.key, this.initialStatus});

  @override
  State<CreateTaskScreen> createState() => _CreateTaskScreenState();
}

class _CreateTaskScreenState extends State<CreateTaskScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  // الحقول الجديدة - New fields
  final _incomingController = TextEditingController(); // الوارد
  final _noteController = TextEditingController(); // الملاحظات

  int? _selectedDepartmentId;
  int? _selectedAssigneeId;
  List<int> _selectedAccessUserIds = [];
  
  // إضافة متغير لتخزين معرف المستخدم الحالي (المنشئ)
  int? _currentUserId;
  DateTime? _startDate;
  DateTime? _dueDate;
  TaskPriority _priority = TaskPriority.medium;
  late TaskStatus _status;
  int? _selectedTaskTypeId;

  List<Department> _departments = [];
  List<User> _users = [];
  List<TaskType> _taskTypes = [];
  final List<File> _selectedFiles = [];
  final List<String> _selectedFileNames = [];
  bool _isLoading = false;
  String? _error;

  // خدمة الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  void initState() {
    super.initState();
    // Set initial status from widget or default to pending
    _status = widget.initialStatus ?? TaskStatus.pending;

    // الحصول على معرف المستخدم الحالي من وحدة التحكم بالمصادقة
    final authController = Get.find<AuthController>();
    _currentUserId = authController.currentUser.value?.id;

    // التحقق من وجود متحكم أنواع المهام أو إنشاؤه
    if (!Get.isRegistered<TaskTypeController>()) {
      Get.put(TaskTypeController());
    }

    _loadData();
  }

  /// تحميل بيانات الإدارات والمستخدمين
  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null; // مسح أي أخطاء سابقة
    });

    // الحصول على وحدة التحكم بالمصادقة
    final authController = Get.find<AuthController>();
    final currentUser = authController.currentUser.value;

    try {
      // التحقق من استخدام قاعدة بيانات في الذاكرة
      // final databaseHelper = DatabaseHelper(); 
      // final isInMemory = databaseHelper.isUsingInMemoryDatabase;

     

      // تحميل الإدارات
      final departmentRepository = DepartmentRepository();
      final departments = await departmentRepository.getActiveDepartments();

      if (departments.isEmpty) {
        // إذا لم توجد إدارات نشطة، حاول الحصول على جميع الإدارات
        _departments = await departmentRepository.getAllDepartments();
        if (_departments.isEmpty) {
          throw Exception('لم يتم العثور على إدارات. يرجى إنشاء إدارة واحدة على الأقل أولاً.');
        }
      } else {
        _departments = departments;
      }

      // تحميل المستخدمين باستخدام وحدة التحكم بالمستخدمين
      try {
        final userController = Get.find<UserController>();
        // تحميل المستخدمين إذا لم يتم تحميلهم بالفعل
        if (userController.users.isEmpty) {
          await userController.loadAllUsers();
        }
        _users = userController.users;
      } catch (e) {
        // إذا لم يتم العثور على وحدة التحكم، استخدم المستودع مباشرة
        final userRepository = UserRepository();
        _users = await userRepository.getAllUsers();
      }

      if (_users.isEmpty) {
        throw Exception('لم يتم العثور على مستخدمين. يرجى إنشاء مستخدم واحد على الأقل أولاً.');
      }

      // تحميل أنواع المهام
      try {
        final taskTypeController = Get.find<TaskTypeController>();
        await taskTypeController.loadTaskTypes();
        _taskTypes = taskTypeController.taskTypes;
      } catch (e) {
        debugPrint('خطأ في تحميل أنواع المهام: $e');
        // استمر حتى لو فشل تحميل أنواع المهام
        _taskTypes = [];
      }

      if (!mounted) return;

      // تعيين إدارة المستخدم الحالي كافتراضية إذا كانت متاحة
      if (currentUser?.departmentId != null) {
        _selectedDepartmentId = currentUser!.departmentId;
      } else if (_departments.isNotEmpty) {
        _selectedDepartmentId = _departments.first.id;
      }

      // إضافة المستخدم الحالي إلى قائمة الوصول افتراضيًا
      if (currentUser != null) {
        _selectedAccessUserIds = [currentUser.id];
      }
    } catch (e) {
      debugPrint('Error loading data for task creation: $e');

      // محاولة تحميل البيانات مرة أخرى
      try {
        debugPrint('محاولة تحميل البيانات مرة أخرى...');

        // محاولة تحميل البيانات من المتحكمات
        if (Get.isRegistered<UserController>()) {
          final userController = Get.find<UserController>();
          await userController.loadAllUsers();
          _users = userController.allUsers;
        }

        // تحميل الأقسام من Repository
        final departmentRepository = DepartmentRepository();
        _departments = await departmentRepository.getAllDepartments();

        if (_departments.isNotEmpty && _users.isNotEmpty) {
          // تم تحميل البيانات بنجاح
          _selectedDepartmentId = _departments.first.id;

          // إضافة المستخدم الحالي لقائمة الوصول
          final authController = Get.find<AuthController>();
          final currentUser = authController.currentUser.value;
          if (currentUser != null) {
            _selectedAccessUserIds = [currentUser.id];
          }

          _error = null;
        } else {
          _error = 'فشل في تحميل البيانات: $e';
        }
      } catch (fallbackError) {
        debugPrint('Fallback error: $fallbackError');
        _error = 'Error loading data: $e\nFallback error: $fallbackError';
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    // تنظيف الحقول الجديدة - Dispose new fields
    _incomingController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  /// اختيار ملفات للإرفاق بالمهمة
  Future<void> _pickFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(allowMultiple: true);
      if (result != null && result.files.isNotEmpty) {
        setState(() {
          for (final file in result.files) {
            if (file.path != null) {
              final fileObj = File(file.path!);
              // منع تكرار نفس الملف
              if (!_selectedFiles.any((f) => f.path == fileObj.path)) {
                _selectedFiles.add(fileObj);
                _selectedFileNames.add(file.name);
              }
            }
          }
        });
      }
    } catch (e) {
      debugPrint('خطأ في اختيار الملفات: $e');
      if (mounted) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء اختيار الملفات: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.statusCancelled.withAlpha(26),
          colorText: AppColors.statusCancelled,
        );
      }
    }
  }

  /// إزالة ملف من قائمة الملفات المختارة
  void _removeFile(int index) {
    setState(() {
      _selectedFiles.removeAt(index);
      _selectedFileNames.removeAt(index);
    });
  }

  /// عرض حوار اختيار المستخدمين
  Future<void> _showUserSelectionDialog() async {
    final result = await showMultiUserSelectionDialog(
      context: context,
      users: _users,
      departments: _departments,
      selectedUserIds: _selectedAccessUserIds.map((id) => id.toString()).toList(),
      title: 'اختيار المستخدمين للوصول إلى المهمة',
      subtitle: 'يمكنك اختيار عدة مستخدمين للوصول إلى هذه المهمة',
    );

    if (result != null) {
      setState(() {
        _selectedAccessUserIds = result.map((id) => int.parse(id)).toList();

        // إذا تمت إزالة المستخدم المعين، قم بإزالة التعيين
        if (_selectedAssigneeId != null && !_selectedAccessUserIds.contains(_selectedAssigneeId!)) {
          _selectedAssigneeId = null;
        }
      });
    }
  }

  /// عرض حوار اختيار مستخدم واحد للتعيين
  Future<void> _showAssigneeSelectionDialog() async {
    final result = await showSingleUserSelectionDialog(
      context: context,
      users: _users,
      departments: _departments,
      title: 'اختيار مستخدم لتعيينه للمهمة',
      subtitle: 'اختر المستخدم المسؤول عن تنفيذ هذه المهمة',
    );

    if (result != null) {
      setState(() {
        _selectedAssigneeId = int.parse(result);

        // إضافة المستخدم المعين إلى قائمة الوصول إذا لم يكن موجودًا بالفعل
        if (!_selectedAccessUserIds.contains(_selectedAssigneeId!)) {
          _selectedAccessUserIds.add(_selectedAssigneeId!);
        }
      });
    }
  }

  // /// Implementation of showUserSelectionDialog as a private method - COMMENTED OUT
  // /// تم تعليق هذه الدالة واستبدالها بـ UserSelectionDialog المحسن
  // Future<List<int>?> showUserSelectionDialog({
  //   required BuildContext context,
  //   required List<User> users,
  //   required List<Department> departments,
  //   required List<int> selectedUserIds,
  //   required String title,
  // }) {
  //   final List<int> tempSelectedUserIds = List<int>.from(selectedUserIds);

  //   return showDialog<List<int>>(
  //     context: context,
  //     builder: (BuildContext context) {
  //       return AlertDialog(
  //         title: Text(title),
  //         content: SizedBox(
  //           width: double.maxFinite,
  //           child: StatefulBuilder(
  //             builder: (BuildContext context, StateSetter setState) {
  //               return ListView(
  //                 shrinkWrap: true,
  //                 children: users.map((user) {
  //                   final bool isSelected = tempSelectedUserIds.contains(user.id);
  //                   return CheckboxListTile(
  //                     title: Text(user.name),
  //                     value: isSelected,
  //                     onChanged: (bool? checked) {
  //                       setState(() {
  //                         if (checked == true) {
  //                           tempSelectedUserIds.add(user.id);
  //                         } else {
  //                           tempSelectedUserIds.remove(user.id);
  //                         }
  //                       });
  //                     },
  //                   );
  //                 }).toList(),
  //               );
  //             },
  //           ),
  //         ),
  //         actions: <Widget>[
  //           TextButton(
  //             child: const Text('إلغاء'),
  //             onPressed: () {
  //               Navigator.of(context).pop(null);
  //             },
  //           ),
  //           ElevatedButton(
  //             child: const Text('موافق'),
  //             onPressed: () {
  //               Navigator.of(context).pop(tempSelectedUserIds);
  //             },
  //           ),
  //         ],
  //       );
  //     },
  //   );
  // }

  // /// Implementation of showSingleUserSelectionDialog as a private method - COMMENTED OUT
  // /// تم تعليق هذه الدالة واستبدالها بـ UserSelectionDialog المحسن
  // Future<int?> showSingleUserSelectionDialog({
  //   required BuildContext context,
  //   required List<User> users,
  //   required List<Department> departments,
  //   required int? selectedUserId,
  //   required String title,
  // }) {
  //   int? tempSelectedUserId = selectedUserId;

  //   return showDialog<int>(
  //     context: context,
  //     builder: (BuildContext context) {
  //       return AlertDialog(
  //         title: Text(title),
  //         content: SizedBox(
  //           width: double.maxFinite,
  //           child: StatefulBuilder(
  //             builder: (BuildContext context, StateSetter setState) {
  //               return ListView(
  //                 shrinkWrap: true,
  //                 children: users.map((user) {
  //                   return RadioListTile<int>(
  //                     title: Text(user.name),
  //                     value: user.id,
  //                     groupValue: tempSelectedUserId,
  //                     onChanged: (int? value) {
  //                       setState(() {
  //                         tempSelectedUserId = value;
  //                       });
  //                     },
  //                   );
  //                 }).toList(),
  //               );
  //             },
  //           ),
  //         ),
  //         actions: <Widget>[
  //           TextButton(
  //             child: const Text('إلغاء'),
  //             onPressed: () {
  //               Navigator.of(context).pop(null);
  //             },
  //           ),
  //           ElevatedButton(
  //             child: const Text('موافق'),
  //             onPressed: () {
  //               Navigator.of(context).pop(tempSelectedUserId);
  //             },
  //           ),
  //         ],
  //       );
  //     },
  //   );
  // }

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );

    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;

        // If due date is before start date, update it
        if (_dueDate != null && _dueDate!.isBefore(_startDate!)) {
          _dueDate = _startDate!.add(const Duration(days: 7));
        }
      });
    }
  }

  Future<void> _selectDueDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? (_startDate != null ? _startDate!.add(const Duration(days: 7)) : DateTime.now().add(const Duration(days: 7))),
      firstDate: _startDate ?? DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );

    if (picked != null && picked != _dueDate) {
      setState(() {
        _dueDate = picked;
      });
    }
  }

  /// إنشاء مهمة جديدة بالمعلومات المدخلة
  Future<void> _createTask() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedDepartmentId == null) {
        Get.snackbar(
          'خطأ',
          'يرجى اختيار إدارة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.statusCancelled.withAlpha(26),
          colorText: AppColors.statusCancelled,
        );
        return;
      }

      if (_selectedAccessUserIds.isEmpty) {
        Get.snackbar(
          'خطأ',
          'يرجى اختيار مستخدم واحد على الأقل للوصول',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.statusCancelled.withAlpha(26),
          colorText: AppColors.statusCancelled,
        );
        return;
      }

      // التأكد من إضافة المستخدم الحالي (المنشئ) إلى قائمة الوصول إذا لم يكن موجودًا
      if (_currentUserId != null && !_selectedAccessUserIds.contains(_currentUserId)) {
        _selectedAccessUserIds.add(_currentUserId!);
      }

      try {
        final taskController = Get.find<TaskController>();

        final success = await taskController.createTask(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          assigneeId: _selectedAssigneeId,
          departmentId: _selectedDepartmentId,
          accessUserIds: _selectedAccessUserIds.map((id) => id.toString()).toList(),
          startDate: _startDate,
          dueDate: _dueDate,
          priority: _priority.stringValue,
          status: _status.stringValue,
          taskTypeId: _selectedTaskTypeId,
          attachments: [], // تعطيل المرفقات في الويب
          // الحقول الجديدة - New fields
          incoming: _incomingController.text.trim().isNotEmpty ? _incomingController.text.trim() : null,
          note: _noteController.text.trim().isNotEmpty ? _noteController.text.trim() : null,
        );

        if (mounted) {
          if (success) {
            // إشعار بنجاح إنشاء المهمة
            Get.snackbar(
              'نجاح',
              'تم إنشاء المهمة بنجاح',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: AppColors.statusCompleted.withAlpha(26),
              colorText: AppColors.statusCompleted,
              duration: const Duration(seconds: 2),
            );

            // الحصول على معرف المهمة المُنشأة والانتقال لتفاصيلها
            final createdTask = taskController.currentTask;
            if (createdTask != null && createdTask.id > 0) {
              Get.offNamed('/task/detail', arguments: createdTask.id.toString());
            } else {
              Get.back(result: true);
            }
          } else {
            Get.snackbar(
              'خطأ',
              'فشل إنشاء المهمة: ${taskController.error}',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: AppColors.statusCancelled.withAlpha(26),
              colorText: AppColors.statusCancelled,
            );
          }
        }
      } catch (e) {
        if (mounted) {
          Get.snackbar(
            'خطأ',
            'حدث خطأ أثناء إنشاء المهمة: $e',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.statusCancelled.withAlpha(26),
            colorText: AppColors.statusCancelled,
          );
        }
      }
    }
  }

  // /// الحصول على نوع الملف من اسمه
  // String _getFileType(String fileName) {
  //   final extension = fileName.split('.').last.toLowerCase();
  //
  //   // أنواع الصور
  //   if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
  //     return 'image/$extension';
  //   }
  //
  //   // أنواع المستندات
  //   if (extension == 'pdf') return 'application/pdf';
  //   if (['doc', 'docx'].contains(extension)) return 'application/msword';
  //   if (['xls', 'xlsx'].contains(extension)) return 'application/vnd.ms-excel';
  //   if (['ppt', 'pptx'].contains(extension)) return 'application/vnd.ms-powerpoint';
  //
  //   // أنواع الفيديو
  //   if (['mp4', 'avi', 'mov', 'wmv'].contains(extension)) {
  //     return 'video/$extension';
  //   }
  //
  //   // أنواع الصوت
  //   if (['mp3', 'wav', 'ogg'].contains(extension)) {
  //     return 'audio/$extension';
  //   }
  //
  //   // النوع الافتراضي
  //   return 'application/octet-stream';
  // }

  /// تحويل قيمة اللون النصية إلى لون
  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        String hexColor = colorString.replaceAll('#', '');
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }
        return Color(int.parse('0x$hexColor'));
      }
      return AppColors.primary;
    } catch (e) {
      return AppColors.textSecondary;
    }
  }

  /// Builds the create task screen UI
  @override
  Widget build(BuildContext context) {
    // Note: TaskController is used in Obx widgets below

    return Scaffold(
      appBar: AppBar(
        title: Text('إنشاء مهمة جديدة', style: TextStyle(color: AppColors.textPrimary)),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: AppColors.statusCancelled,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'خطأ في تحميل البيانات',
                          style: AppStyles.titleLarge,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.statusCancelled.withAlpha(26),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: AppColors.statusCancelled.withAlpha(77)),
                          ),
                          child: Column(
                            children: [
                              Text(
                                _error!,
                                style: AppStyles.bodyMedium.copyWith(color: AppColors.statusCancelled),
                                textAlign: TextAlign.center,
                              ),
                              if (_error!.contains('MissingPluginException') ||
                                  _error!.contains('getApplicationDocumentsDirectory'))
                                Padding(
                                  padding: const EdgeInsets.only(top: 16),
                                  child: Text(
                                    'تم تفعيل وضع الذاكرة المؤقتة. البيانات لن تُحفظ بعد إغلاق التطبيق.',
                                    style: AppStyles.bodyMedium.copyWith(
                                      color: AppColors.statusPending,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: _loadData,
                          icon: const Icon(Icons.refresh),
                          label: Text('إعادة المحاولة', style: TextStyle(color: AppColors.white)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: AppColors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // حقل العنوان
                        TextFormField(
                          controller: _titleController,
                          style: TextStyle(color: AppColors.textPrimary),
                          decoration: AppStyles.inputDecoration(
                            labelText: 'عنوان المهمة',
                            prefixIcon: const Icon(Icons.title),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال عنوان للمهمة';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // حقل الوصف
                        TextFormField(
                          controller: _descriptionController,
                          style: TextStyle(color: AppColors.textPrimary),
                          decoration: AppStyles.inputDecoration(
                            labelText: 'وصف المهمة',
                            prefixIcon: const Icon(Icons.description),
                          ),
                          maxLines: 5,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال وصف للمهمة';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // الحقول الجديدة - New fields
                        // حقل الوارد - Incoming field
                        TextFormField(
                          controller: _incomingController,
                          style: TextStyle(color: AppColors.textPrimary),
                          decoration: AppStyles.inputDecoration(
                            labelText: 'الوارد (اختياري)',
                            prefixIcon: const Icon(Icons.input),
                            hintText: 'معلومات إضافية حول مصدر المهمة أو الطلب الوارد',
                          ),
                          maxLines: 2,
                          // هذا الحقل اختياري - This field is optional
                        ),
                        const SizedBox(height: 16),

                        // حقل الملاحظات - Note field
                        TextFormField(
                          controller: _noteController,
                          style: TextStyle(color: AppColors.textPrimary),
                          decoration: AppStyles.inputDecoration(
                            labelText: 'ملاحظات (اختياري)',
                            prefixIcon: const Icon(Icons.note),
                            hintText: 'ملاحظات إضافية حول المهمة',
                          ),
                          maxLines: 3,
                          // هذا الحقل اختياري - This field is optional
                        ),
                        const SizedBox(height: 16),

                        // قائمة الإدارات
                        DropdownButtonFormField<int>(
                          decoration: AppStyles.inputDecoration(
                            labelText: 'الإدارة',
                            prefixIcon: const Icon(Icons.business),
                          ),
                          value: _selectedDepartmentId,
                          items: _departments.map((department) {
                            return DropdownMenuItem<int>(
                              value: department.id,
                              child: Text(department.name, style: TextStyle(color: AppColors.textPrimary)),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedDepartmentId = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),

                        // قائمة أنواع المهام
                        DropdownButtonFormField<int>(
                          decoration: AppStyles.inputDecoration(
                            labelText: 'نوع المهمة (اختياري)',
                            prefixIcon: const Icon(Icons.category),
                          ),
                          value: _selectedTaskTypeId,
                          items: [
                            DropdownMenuItem<int>(
                              value: null,
                              child: Text('بدون نوع', style: TextStyle(color: AppColors.textPrimary)),
                            ),
                            ..._taskTypes.map((taskType) {
                              return DropdownMenuItem<int>(
                                value: taskType.id,
                                child: Row(
                                  children: [
                                    if (taskType.color != null)
                                      Container(
                                        width: 16,
                                        height: 16,
                                        margin: const EdgeInsets.only(left: 8),
                                        decoration: BoxDecoration(
                                          color: _parseColor(taskType.color!),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                    Text(taskType.name, style: TextStyle(color: AppColors.textPrimary)),
                                  ],
                                ),
                              );
                            }),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedTaskTypeId = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),

                        // Assignee selection
                        InkWell(
                          onTap: _showAssigneeSelectionDialog,
                          child: InputDecorator(
                            decoration: AppStyles.inputDecoration(
                              labelText: 'تعيين إلى (اختياري)',
                              prefixIcon: const Icon(Icons.person),
                              suffixIcon: const Icon(Icons.arrow_drop_down),
                            ),
                            child: Text(
                              _selectedAssigneeId != null
                                  ? (_users.any((u) => u.id == _selectedAssigneeId)
                                      ? _users.firstWhere((u) => u.id == _selectedAssigneeId).name
                                      : 'مستخدم: $_selectedAssigneeId')
                                  : 'غير معين',
                              style: _selectedAssigneeId != null
                                  ? AppStyles.bodyMedium
                                  : AppStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Access users section
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'المستخدمون بالوصول',
                              style: AppStyles.titleMedium,
                            ),
                            TextButton.icon(
                              onPressed: _showUserSelectionDialog,
                              icon: const Icon(Icons.people),
                              label: Text('اختيار المستخدمين', style: TextStyle(color: AppColors.white)),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.border),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: _selectedAccessUserIds.isEmpty
                              ? Center(
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Text(
                                      'لم يتم اختيار أي مستخدمين للوصول',
                                      style: TextStyle(color: AppColors.textSecondary),
                                    ),
                                  ),
                                )
                              : Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  children: _selectedAccessUserIds.map((userId) {
                                    // البحث عن المستخدم بطريقة آمنة
                                    final userExists = _users.any((u) => u.id == userId);
                                    if (!userExists) {
                                      // إذا لم يكن المستخدم موجودًا، نعرض شريحة بسيطة بالمعرف
                                      return Chip(
                                        label: Text('مستخدم: $userId', style: TextStyle(color: AppColors.textPrimary)),
                                        onDeleted: () {
                                          setState(() {
                                            _selectedAccessUserIds.remove(userId);
                                          });
                                        },
                                        backgroundColor: AppColors.background,
                                      );
                                    }

                                    // إذا كان المستخدم موجودًا، نعرض معلوماته
                                    final user = _users.firstWhere((u) => u.id == userId);
                                    return Chip(
                                      label: Text(user.name, style: TextStyle(color: AppColors.textPrimary)),
                                      onDeleted: userId != _selectedAssigneeId
                                          ? () {
                                              setState(() {
                                                _selectedAccessUserIds.remove(userId);
                                              });
                                            }
                                          : null,
                                      backgroundColor: userId == _selectedAssigneeId
                                          ? AppColors.primary.withAlpha(51)
                                          : AppColors.background,
                                    );
                                  }).toList(),
                                ),
                        ),

                        // File attachments section
                        const SizedBox(height: 24),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'المرفقات',
                              style: AppStyles.titleMedium,
                            ),
                            if (_permissionService.canUploadAttachments())
                              TextButton.icon(
                                onPressed: _pickFiles,
                                icon: const Icon(Icons.attach_file),
                                label: Text('إضافة مرفقات', style: TextStyle(color: AppColors.white)),
                              ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.border),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: _selectedFiles.isEmpty
                              ? Center(
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Text(
                                      'لم يتم إضافة أي مرفقات',
                                      style: TextStyle(color: AppColors.textSecondary),
                                    ),
                                  ),
                                )
                              : ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: _selectedFiles.length,
                                  itemBuilder: (context, index) {
                                    final fileName = _selectedFileNames[index];
                                    final fileExtension = fileName.split('.').last.toLowerCase();

                                    IconData fileIcon;
                                    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(fileExtension)) {
                                      fileIcon = Icons.image;
                                    } else if (fileExtension == 'pdf') {
                                      fileIcon = Icons.picture_as_pdf;
                                    } else if (['doc', 'docx'].contains(fileExtension)) {
                                      fileIcon = Icons.description;
                                    } else if (['xls', 'xlsx'].contains(fileExtension)) {
                                      fileIcon = Icons.table_chart;
                                    } else if (['ppt', 'pptx'].contains(fileExtension)) {
                                      fileIcon = Icons.slideshow;
                                    } else if (['mp4', 'avi', 'mov', 'wmv'].contains(fileExtension)) {
                                      fileIcon = Icons.video_file;
                                    } else if (['mp3', 'wav', 'ogg'].contains(fileExtension)) {
                                      fileIcon = Icons.audio_file;
                                    } else {
                                      fileIcon = Icons.insert_drive_file;
                                    }

                                    return ListTile(
                                      leading: Icon(fileIcon),
                                      title: Text(fileName, style: TextStyle(color: AppColors.textPrimary)),
                                      trailing: _permissionService.canDeleteAttachments()
                                          ? IconButton(
                                              icon: Icon(Icons.delete, color: AppColors.statusCancelled),
                                              onPressed: () => _removeFile(index),
                                            )
                                          : null,
                                    );
                                  },
                                ),
                        ),
                        const SizedBox(height: 24),

                        // قسم التواريخ
                        Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectStartDate(context),
                                child: InputDecorator(
                                  decoration: AppStyles.inputDecoration(
                                    labelText: 'تاريخ البدء (اختياري)',
                                    prefixIcon: const Icon(Icons.calendar_today),
                                  ),
                                  child: Text(
                                    _startDate != null
                                        ? DateFormat('yyyy/MM/dd').format(_startDate!)
                                        : 'اختر تاريخ',
                                    style: _startDate != null
                                        ? AppStyles.bodyMedium
                                        : AppStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectDueDate(context),
                                child: InputDecorator(
                                  decoration: AppStyles.inputDecoration(
                                    labelText: 'تاريخ الاستحقاق (اختياري)',
                                    prefixIcon: const Icon(Icons.event),
                                  ),
                                  child: Text(
                                    _dueDate != null
                                        ? DateFormat('yyyy/MM/dd').format(_dueDate!)
                                        : 'اختر تاريخ',
                                    style: _dueDate != null
                                        ? AppStyles.bodyMedium
                                        : AppStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Priority section
                        Text(
                          'الأولوية',
                          style: AppStyles.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        SegmentedButton<TaskPriority>(
                          segments: [
                            ButtonSegment<TaskPriority>(
                              value: TaskPriority.low,
                              label: Text('منخفضة', style: TextStyle(color: AppColors.textPrimary)),
                              icon: Icon(Icons.arrow_downward, color: AppColors.textSecondary),
                            ),
                            ButtonSegment<TaskPriority>(
                              value: TaskPriority.medium,
                              label: Text('متوسطة', style: TextStyle(color: AppColors.textPrimary)),
                              icon: Icon(Icons.remove, color: AppColors.textSecondary),
                            ),
                            ButtonSegment<TaskPriority>(
                              value: TaskPriority.high,
                              label: Text('عالية', style: TextStyle(color: AppColors.textPrimary)),
                              icon: Icon(Icons.arrow_upward, color: AppColors.textSecondary),
                            ),
                            ButtonSegment<TaskPriority>(
                              value: TaskPriority.urgent,
                              label: Text('عاجلة', style: TextStyle(color: AppColors.textPrimary)),
                              icon: Icon(Icons.priority_high, color: AppColors.textSecondary),
                            ),
                          ],
                          selected: {_priority},
                          onSelectionChanged: (Set<TaskPriority> newSelection) {
                            setState(() {
                              _priority = newSelection.first;
                            });
                          },
                        ),

                        // Status section
                        const SizedBox(height: 24),
                        Text(
                          'الحالة',
                          style: AppStyles.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        SegmentedButton<TaskStatus>(
                          segments: TaskStatus.values.map((status) {
                            return ButtonSegment<TaskStatus>(
                              value: status,
                              label: Text(status.displayNameAr, style: TextStyle(color: AppColors.textPrimary)),
                              icon: Icon(status.icon),
                            );
                          }).toList(),
                          selected: {_status},
                          onSelectionChanged: (Set<TaskStatus> newSelection) {
                            setState(() {
                              _status = newSelection.first;
                            });
                          },
                        ),
                        const SizedBox(height: 32),

                        // Error message
                        Obx(() {
                          final taskController = Get.find<TaskController>();
                          if (taskController.error.isNotEmpty) {
                            return Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: AppColors.statusCancelled.withAlpha(26),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                taskController.error,
                                style: TextStyle(color: AppColors.statusCancelled),
                                textAlign: TextAlign.center,
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        }),

                        const SizedBox(height: 16),

                        if(Get.find<UnifiedPermissionService>().canCreateTask())
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _createTask,
                              style: AppStyles.primaryButtonStyle,
                              child: Text('إنشاء المهمة', style: TextStyle(color: AppColors.white)),
                            ),
                          ),
                        const SizedBox(height: 16),
                    
                      ],
                    ),
                  ),
                ),
    );
  }
}
