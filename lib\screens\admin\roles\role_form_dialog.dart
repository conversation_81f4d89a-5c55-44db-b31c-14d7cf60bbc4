import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../models/role_model.dart';
import '../../../models/permission_models.dart';
import '../../../controllers/admin_controller.dart';
import '../../../controllers/auth_controller.dart';
import '../../../services/api/roles_api_service.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_dialog_widget.dart';
import '../shared/admin_form_widget.dart';

/// حوار إنشاء أو تعديل دور
class RoleFormDialog extends StatefulWidget {
  final Role? role; // null للإنشاء، Role للتعديل
  final VoidCallback? onSuccess;

  const RoleFormDialog({
    super.key,
    this.role,
    this.onSuccess,
  });

  @override
  State<RoleFormDialog> createState() => _RoleFormDialogState();
}

class _RoleFormDialogState extends State<RoleFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final AdminController _adminController = Get.find<AdminController>();
  final AuthController _authController = Get.find<AuthController>();
  final RolesApiService _rolesApiService = RolesApiService();

  // Controllers للحقول
  late final TextEditingController _nameController;
  late final TextEditingController _displayNameController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _levelController;

  // حالة الحوار
  final RxBool _isLoading = false.obs;
  final RxBool _isActive = true.obs;
  final RxSet<int> _selectedPermissions = <int>{}.obs;

  // متغيرات للتحقق من صحة البيانات
  String? _nameError;
  String? _levelError;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadPermissions();
  }

  /// تهيئة Controllers
  void _initializeControllers() {
    final role = widget.role;
    _nameController = TextEditingController(text: role?.name ?? '');
    _displayNameController = TextEditingController(text: role?.displayName ?? '');
    _descriptionController = TextEditingController(text: role?.description ?? '');
    _levelController = TextEditingController(text: role?.level.toString() ?? '1');
    _isActive.value = role?.isActive ?? true;

    // تحميل الصلاحيات المحددة للدور (في حالة التعديل)
    if (role != null) {
      _loadRolePermissions(role.id);
    }
  }

  /// تحميل جميع الصلاحيات المتاحة
  Future<void> _loadPermissions() async {
    try {
      await _adminController.loadPermissions();
    } catch (e) {
      debugPrint('خطأ في تحميل الصلاحيات: $e');
    }
  }

  /// تحميل صلاحيات الدور (للتعديل)
  Future<void> _loadRolePermissions(int roleId) async {
    try {
      final permissions = await _rolesApiService.getRolePermissions(roleId);
      _selectedPermissions.assignAll(permissions.map((p) => p.id));
    } catch (e) {
      debugPrint('خطأ في تحميل صلاحيات الدور: $e');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _displayNameController.dispose();
    _descriptionController.dispose();
    _levelController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.role != null;
    final title = isEditing ? 'تعديل الدور' : 'إنشاء دور جديد';
    final theme = Theme.of(context);

    return Dialog(
      backgroundColor: theme.colorScheme.surface,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(
          maxWidth: 800,
          maxHeight: 700,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header محسن مع تباين أفضل
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                border: Border(
                  bottom: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      isEditing ? Icons.edit : Icons.add,
                      color: theme.colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: Icon(
                      Icons.close,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: theme.colorScheme.surface.withValues(alpha: 0.5),
                    ),
                  ),
                ],
              ),
            ),

            // Content مع تحسين التخطيط
            Expanded(
              child: Obx(() {
                if (_isLoading.value) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'جاري الحفظ...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // معلومات الدور الأساسية
                        _buildBasicInfoSection(),

                        const SizedBox(height: 32),

                        // الصلاحيات
                        _buildPermissionsSection(),
                      ],
                    ),
                  ),
                );
              }),
            ),

            // Actions محسنة مع تباين أفضل
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
                border: Border(
                  top: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () => Get.back(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      side: BorderSide(color: theme.colorScheme.outline),
                    ),
                    child: Text(
                      'إلغاء',
                      style: TextStyle(color: theme.colorScheme.onSurface),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Obx(() => ElevatedButton(
                    onPressed: _isLoading.value ? null : _saveRole,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      elevation: 2,
                    ),
                    child: _isLoading.value
                        ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: theme.colorScheme.onPrimary,
                            ),
                          )
                        : Text(
                            isEditing ? 'تحديث' : 'إنشاء',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                  )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم المعلومات الأساسية
  Widget _buildBasicInfoSection() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.info_outline,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'المعلومات الأساسية',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // اسم الدور
          AdminTextField(
            controller: _nameController,
            label: 'اسم الدور',
            hint: 'مثال: manager, supervisor',
            enabled: true,
            isRequired: true,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'اسم الدور مطلوب';
              }
              if (value.trim().length < 3) {
                return 'اسم الدور يجب أن يكون 3 أحرف على الأقل';
              }
              return _nameError;
            },
          ),

          const SizedBox(height: 16),

          // الاسم المعروض
          AdminTextField(
            controller: _displayNameController,
            label: 'الاسم المعروض',
            hint: 'مثال: مدير، مشرف',
            isRequired: true,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'الاسم المعروض مطلوب';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // الوصف
          AdminTextField(
            controller: _descriptionController,
            label: 'الوصف',
            hint: 'وصف مختصر للدور ومسؤولياته',
            maxLines: 3,
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              // مستوى الدور
              Expanded(
                child: AdminTextField(
                  controller: _levelController,
                  label: 'مستوى الدور',
                  hint: '1-100',
                  keyboardType: TextInputType.number,
                  isRequired: true,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'مستوى الدور مطلوب';
                    }
                    final level = int.tryParse(value);
                    if (level == null || level < 1 || level > 100) {
                      return 'المستوى يجب أن يكون بين 1 و 100';
                    }
                    return _levelError;
                  },
                ),
              ),

              const SizedBox(width: 16),

              // حالة الدور
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: theme.colorScheme.primary.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Obx(() => AdminSwitchField(
                    label: 'الدور نشط',
                    value: _isActive.value,
                    onChanged: (value) => _isActive.value = value,
                  )),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قسم الصلاحيات
  Widget _buildPermissionsSection() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header محسن مع أزرار التحكم
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.security,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'الصلاحيات',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
                // أزرار التحكم محسنة
                Container(
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: theme.colorScheme.outline.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextButton.icon(
                        onPressed: _selectAllPermissions,
                        icon: Icon(
                          Icons.check_box,
                          color: theme.colorScheme.primary,
                          size: 18,
                        ),
                        label: Text(
                          'تحديد الكل',
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 24,
                        color: theme.colorScheme.outline.withValues(alpha: 0.3),
                      ),
                      TextButton.icon(
                        onPressed: _clearAllPermissions,
                        icon: Icon(
                          Icons.check_box_outline_blank,
                          color: theme.colorScheme.error,
                          size: 18,
                        ),
                        label: Text(
                          'إلغاء الكل',
                          style: TextStyle(
                            color: theme.colorScheme.error,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // عرض الصلاحيات
          Obx(() {
            final permissions = _adminController.permissions;
            if (permissions.isEmpty) {
              return Container(
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: theme.colorScheme.outline.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.security_outlined,
                        color: theme.colorScheme.onSurfaceVariant,
                        size: 48,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'لا توجد صلاحيات متاحة',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            // تجميع الصلاحيات حسب المجموعة
            final groupedPermissions = <String, List<Permission>>{};
            for (final permission in permissions) {
              final group = permission.permissionGroup;
              groupedPermissions.putIfAbsent(group, () => []).add(permission);
            }

            return Column(
              children: groupedPermissions.entries.map((entry) {
                return _buildPermissionGroup(entry.key, entry.value);
              }).toList(),
            );
          }),
        ],
      ),
    );
  }

  /// بناء مجموعة صلاحيات
  Widget _buildPermissionGroup(String groupName, List<Permission> permissions) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        childrenPadding: const EdgeInsets.only(bottom: 8),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: theme.colorScheme.secondaryContainer,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.folder_outlined,
                color: theme.colorScheme.onSecondaryContainer,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                groupName,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4, right: 36),
          child: Obx(() {
            final selectedCount = permissions
                .where((p) => _selectedPermissions.contains(p.id))
                .length;
            final isAllSelected = selectedCount == permissions.length;
            final isPartialSelected = selectedCount > 0 && selectedCount < permissions.length;

            return Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: isAllSelected
                        ? theme.colorScheme.primary.withValues(alpha: 0.1)
                        : isPartialSelected
                            ? theme.colorScheme.tertiary.withValues(alpha: 0.1)
                            : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isAllSelected
                          ? theme.colorScheme.primary.withValues(alpha: 0.3)
                          : isPartialSelected
                              ? theme.colorScheme.tertiary.withValues(alpha: 0.3)
                              : theme.colorScheme.outline.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    '$selectedCount من ${permissions.length} محدد',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isAllSelected
                          ? theme.colorScheme.primary
                          : isPartialSelected
                              ? theme.colorScheme.tertiary
                              : theme.colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (isAllSelected) ...[
                  const SizedBox(width: 8),
                  Icon(
                    Icons.check_circle,
                    color: theme.colorScheme.primary,
                    size: 16,
                  ),
                ] else if (isPartialSelected) ...[
                  const SizedBox(width: 8),
                  Icon(
                    Icons.remove_circle,
                    color: theme.colorScheme.tertiary,
                    size: 16,
                  ),
                ],
              ],
            );
          }),
        ),
        children: permissions.map((permission) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Obx(() => CheckboxListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              title: Text(
                permission.name,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              subtitle: permission.description != null
                  ? Text(
                      permission.description!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    )
                  : null,
              value: _selectedPermissions.contains(permission.id),
              activeColor: theme.colorScheme.primary,
              checkColor: theme.colorScheme.onPrimary,
              onChanged: (value) {
                if (value == true) {
                  _selectedPermissions.add(permission.id);
                } else {
                  _selectedPermissions.remove(permission.id);
                }
              },
            )),
          );
        }).toList(),
      ),
    );
  }

  /// تحديد جميع الصلاحيات
  void _selectAllPermissions() {
    final allPermissionIds = _adminController.permissions.map((p) => p.id).toSet();
    _selectedPermissions.assignAll(allPermissionIds);
  }

  /// إلغاء تحديد جميع الصلاحيات
  void _clearAllPermissions() {
    _selectedPermissions.clear();
  }

  /// حفظ الدور
  Future<void> _saveRole() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    _isLoading.value = true;
    _nameError = null;
    _levelError = null;

    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final isEditing = widget.role != null;

      if (isEditing) {
        // تحديث الدور
        await _updateRole(currentUser.id);
      } else {
        // إنشاء دور جديد
        await _createRole(currentUser.id);
      }

      // إعادة تحميل الأدوار
      await _adminController.loadRoles();

      // إغلاق الحوار وإظهار رسالة نجاح
      Get.back();
      AdminMessageDialog.showSuccess(
        title: 'تم بنجاح',
        message: isEditing ? 'تم تحديث الدور بنجاح' : 'تم إنشاء الدور بنجاح',
      );

      // استدعاء callback النجاح
      widget.onSuccess?.call();

    } catch (e) {
      debugPrint('خطأ في حفظ الدور: $e');
      
      // معالجة أخطاء محددة
      final errorMessage = e.toString();
      if (errorMessage.contains('اسم الدور موجود')) {
        setState(() {
          _nameError = 'اسم الدور موجود بالفعل';
        });
        _formKey.currentState!.validate();
      } else if (errorMessage.contains('مستوى الدور موجود')) {
        setState(() {
          _levelError = 'مستوى الدور موجود بالفعل';
        });
        _formKey.currentState!.validate();
      } else {
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في حفظ الدور: $e',
        );
      }
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء دور جديد
  Future<void> _createRole(int userId) async {
    await _rolesApiService.createRole(
      name: _nameController.text.trim(),
      displayName: _displayNameController.text.trim(),
      description: _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim(),
      level: int.parse(_levelController.text),
      isActive: _isActive.value,
      createdBy: userId,
      defaultPermissionIds: _selectedPermissions.toList(),
    );
  }

  /// تحديث دور موجود
  Future<void> _updateRole(int userId) async {
    final role = widget.role!;
    
    // تحديث معلومات الدور
    await _rolesApiService.updateRole(
      id: role.id,
      displayName: _displayNameController.text.trim(),
      description: _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim(),
      level: int.parse(_levelController.text),
      isActive: _isActive.value,
      updatedBy: userId,
    );

    // تحديث الصلاحيات
    await _rolesApiService.grantMultiplePermissionsToRole(
      roleId: role.id,
      permissionIds: _selectedPermissions.toList(),
      userId: userId,
      replaceExisting: true, // استبدال الصلاحيات الحالية
    );

    // 🔄 إعادة تحميل صلاحيات المستخدم الحالي إذا تم تعديل دوره
    try {
      final permissionService = Get.find<UnifiedPermissionService>();
      await permissionService.refreshCurrentUserPermissions();
      debugPrint('✅ تم تحديث صلاحيات المستخدم الحالي بعد تعديل الدور');
    } catch (e) {
      debugPrint('⚠️ خطأ في تحديث صلاحيات المستخدم: $e');
    }
  }
}
