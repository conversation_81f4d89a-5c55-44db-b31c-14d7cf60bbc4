import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/models/subtask_models.dart';
import 'package:flutter_application_2/screens/widgets/common/loading_indicator.dart';
import 'package:flutter_application_2/screens/widgets/common/empty_state_widget.dart';
import 'package:get/get.dart';
import '../../controllers/subtasks_controller.dart';
import '../../models/task_model.dart';
import '../../services/unified_permission_service.dart';


/// علامة تبويب المهام الفرعية
/// تعرض قائمة المهام الفرعية للمهمة الحالية وتتيح إضافة وتعديل وحذف المهام الفرعية
class SubtasksTab extends GetView<SubtasksController> {
  final Task task;
  final TextEditingController _titleController = TextEditingController();

   SubtasksTab({super.key, required this.task});

  /// تحميل المهام الفرعية
  Future<void> _loadSubtasks() async {
    await controller.getSubtasksByTask(task.id);
  }

  /// تحديث حالة إكمال المهمة الفرعية
  Future<void> _toggleSubtaskCompletion(Subtask subtask, bool isCompleted) async {
    try {
      bool success;
      if (isCompleted) {
        success = await controller.markAsCompleted(subtask.id);
      } else {
        success = await controller.markAsIncomplete(subtask.id);
      }
      if (success) {
        await _loadSubtasks();
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في تحديث حالة المهمة الفرعية',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث حالة المهمة الفرعية',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // عنوان وزر إضافة
        Padding(
          padding: const EdgeInsets.all(16),
          child: LayoutBuilder(
            builder: (context, constraints) {
              // Check if we need to use a responsive layout
              final isSmallScreen = constraints.maxWidth < 400;

              if (isSmallScreen) {
                // Use a column layout for small screens
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المهام الفرعية',
                      style: AppStyles.titleMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w800,
                        shadows: [
                          Shadow(
                            color: AppColors.getShadowColor(0.2),
                            blurRadius: 1,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _showAddSubtaskDialog,
                        icon: const Icon(Icons.add),
                        label: Text('إضافة مهمة فرعية', style: TextStyle(
                          color: AppColors.white,
                          fontWeight: FontWeight.w600,
                        )),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                );
              } else {
                // Use a row layout for larger screens
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  textDirection: TextDirection.rtl,
                  children: [
                    Flexible(
                      child: Text(
                        'المهام الفرعية',
                        style: AppStyles.titleMedium.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w800,
                          shadows: [
                            Shadow(
                              color: AppColors.getShadowColor(0.2),
                              blurRadius: 1,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: _showAddSubtaskDialog,
                      icon: const Icon(Icons.add,color: Colors.white),
                      label: Text('إضافة مهمة فرعية', style: TextStyle(
                        color: AppColors.white,
                        fontWeight: FontWeight.w600,
                      )),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: AppColors.white,
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ),

        // قائمة المهام الفرعية
        Expanded(
          child: Obx(() {
            // تحميل المهام الفرعية عند أول عرض
            if (controller.taskSubtasks.isEmpty && !controller.isLoading) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _loadSubtasks();
              });
            }

            if (controller.isLoading) {
              return const Center(child: LoadingIndicator());
            }

            if (controller.taskSubtasks.isEmpty) {
              return const EmptyStateWidget(
                icon: Icons.task_alt,
                message: 'لا توجد مهام فرعية\nقم بإضافة مهام فرعية لتتبع التقدم بشكل أفضل',
              );
            }

            return ListView.builder(
              itemCount: controller.taskSubtasks.length,
              itemBuilder: (context, index) {
                final subtask = controller.taskSubtasks[index];
                return _buildSubtaskItem(subtask);
              },
            );
          }),
        ),

        // ملخص التقدم
        Obx(() {
          final completedCount = controller.taskSubtasks
              .where((s) => s.isCompleted)
              .length;
          final totalCount = controller.taskSubtasks.length;

          if (totalCount == 0) {
            return const SizedBox.shrink();
          }

          final progress = completedCount / totalCount;

          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surface,
              boxShadow: [
                BoxShadow(
                  color: AppColors.getShadowColor(0.1),
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'التقدم: $completedCount من $totalCount مكتملة',
                      style: AppStyles.bodyMedium.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                        shadows: [
                          Shadow(
                            color: AppColors.getShadowColor(0.1),
                            blurRadius: 0.5,
                            offset: const Offset(0, 0.5),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '${(progress * 100).toInt()}%',
                      style: AppStyles.titleMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w800,
                        shadows: [
                          Shadow(
                            color: AppColors.getShadowColor(0.15),
                            blurRadius: 0.8,
                            offset: const Offset(0, 0.8),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: AppColors.progressBackground,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  minHeight: 8,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  /// بناء عنصر المهمة الفرعية
  Widget _buildSubtaskItem(Subtask subtask) {
      //الصلاحيات
    final permissionService = Get.find<UnifiedPermissionService>();

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // مربع الاختيار للإكمال
                SizedBox(
                  width: 40,
                  child: Checkbox(
                    value: subtask.isCompleted,
                    activeColor: AppColors.primary,
                    onChanged: (value) {
                      if (value != null) {
                        _toggleSubtaskCompletion(subtask, value);
                      }
                    },
                  ),
                ),

                // عنوان المهمة الفرعية
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Text(
                      subtask.title,
                      style: AppStyles.bodyMedium.copyWith(
                        decoration: subtask.isCompleted
                            ? TextDecoration.lineThrough
                            : null,
                        color: subtask.isCompleted
                            ? AppColors.textSecondary
                            : AppColors.textPrimary,
                        fontWeight: subtask.isCompleted
                            ? FontWeight.w500
                            : FontWeight.w700,
                        shadows: subtask.isCompleted ? null : [
                          Shadow(
                            color: AppColors.getShadowColor(0.1),
                            blurRadius: 0.5,
                            offset: const Offset(0, 0.5),
                          ),
                        ],
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                ),

                // قائمة الخيارات
                SizedBox(
                  width: 40,
                  child: PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert),
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          _showEditSubtaskDialog(subtask);
                          break;
                        case 'delete':
                          _showDeleteSubtaskDialog(subtask);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (permissionService.canEditTask())
                        PopupMenuItem<String>(
                          value: 'edit',
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(Icons.edit, size: 18),
                              const SizedBox(width: 8),
                              Text('تعديل', style: TextStyle(
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.w600,
                              )),
                            ],
                          ),
                        ),
                      if (permissionService.canDeleteTask())
                        PopupMenuItem<String>(
                          value: 'delete',
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.delete, size: 18, color: AppColors.statusCancelled),
                              const SizedBox(width: 8),
                              Text('حذف', style: TextStyle(
                                color: AppColors.statusCancelled,
                                fontWeight: FontWeight.w600,
                              )),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),

            // معلومات إضافية
            Padding(
              padding: const EdgeInsets.only(right: 40, left: 16, bottom: 8),
              child: Row(
                children: [
                  Icon(Icons.access_time, size: 14, color: AppColors.textSecondary),
                  const SizedBox(width: 4),
                  Text(
                    'تم الإنشاء: ${subtask.createdAtDateTime.day}/${subtask.createdAtDateTime.month}/${subtask.createdAtDateTime.year}',
                    style: AppStyles.labelSmall.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                      shadows: [
                        Shadow(
                          color: AppColors.getShadowColor(0.08),
                          blurRadius: 0.3,
                          offset: const Offset(0, 0.3),
                        ),
                      ],
                    ),
                  ),
                  if (subtask.isCompleted && subtask.completedAtDateTime != null) ...[
                    const SizedBox(width: 16),
                    Icon(Icons.check_circle, size: 14, color: AppColors.statusCompleted),
                    const SizedBox(width: 4),
                    Text(
                      'مكتمل: ${subtask.completedAtDateTime!.day}/${subtask.completedAtDateTime!.month}/${subtask.completedAtDateTime!.year}',
                      style: AppStyles.labelSmall.copyWith(
                        color: AppColors.statusCompleted,
                        fontWeight: FontWeight.w600,
                        shadows: [
                          Shadow(
                            color: AppColors.getShadowColor(0.08),
                            blurRadius: 0.3,
                            offset: const Offset(0, 0.3),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض مربع حوار إضافة مهمة فرعية
  void _showAddSubtaskDialog() {
    _titleController.clear();
    Get.dialog(
      Dialog(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'إضافة مهمة فرعية',
                style: AppStyles.titleMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w800,
                  shadows: [
                    Shadow(
                      color: AppColors.getShadowColor(0.2),
                      blurRadius: 1,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _titleController,
                style: TextStyle(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
                decoration: InputDecoration(
                  labelText: 'العنوان *',
                  labelStyle: TextStyle(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.border),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.border),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.primary, width: 2.0),
                  ),
                ),
                maxLength: 100,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: Text('إلغاء', style: TextStyle(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    )),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () async {
                      if (_titleController.text.trim().isEmpty) {
                        Get.snackbar(
                          'خطأ',
                          'يرجى إدخال عنوان للمهمة الفرعية',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                        return;
                      }
                      try {
                        debugPrint('محاولة إنشاء مهمة فرعية: TaskId=${task.id}, Title=${_titleController.text.trim()}');
                        final success = await controller.createSubtask(task.id, _titleController.text.trim());

                        // إغلاق الديالوج أولاً
                        Get.back();

                        if (success) {
                          // إعادة تحميل البيانات
                          await _loadSubtasks();

                          // عرض رسالة النجاح
                          Get.snackbar(
                            'نجح',
                            'تم إضافة المهمة الفرعية بنجاح',
                            snackPosition: SnackPosition.BOTTOM,
                            backgroundColor: AppColors.statusCompleted.withAlpha(26),
                            colorText: AppColors.statusCompleted,
                          );
                        } else {
                          Get.snackbar(
                            'خطأ',
                            'فشل في إضافة المهمة الفرعية',
                            snackPosition: SnackPosition.BOTTOM,
                            backgroundColor: AppColors.statusCancelled.withAlpha(26),
                            colorText: AppColors.statusCancelled,
                          );
                        }
                      } catch (e) {
                        debugPrint('❌ خطأ في إضافة المهمة الفرعية: $e');

                        // إغلاق الديالوج في جميع الحالات
                        Get.back();

                        String errorMessage = 'فشل في إضافة المهمة الفرعية';
                        Color backgroundColor = AppColors.statusCancelled;
                        String title = 'خطأ';

                        // تحديد نوع الخطأ لعرض رسالة أوضح
                        if (e.toString().contains('FormatException') ||
                            e.toString().contains('JSON') ||
                            e.toString().contains('type') ||
                            e.toString().contains('تحليل')) {
                          errorMessage = 'تم حفظ المهمة الفرعية بنجاح ولكن حدث خطأ في عرض البيانات. يرجى تحديث الصفحة.';
                          backgroundColor = AppColors.statusPending;
                          title = 'تحذير';
                          debugPrint('💡 المهمة الفرعية تم حفظها في قاعدة البيانات ولكن هناك مشكلة في تحليل الاستجابة');

                          // إعادة تحميل البيانات لإظهار المهمة الفرعية الجديدة
                          await _loadSubtasks();
                        }

                        Get.snackbar(
                          title,
                          errorMessage,
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: backgroundColor,
                          colorText: AppColors.white,
                          duration: const Duration(seconds: 7),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.white,
                    ),
                    child: Text('إضافة', style: TextStyle(
                      color: AppColors.white,
                      fontWeight: FontWeight.w600,
                    )),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض مربع حوار تعديل مهمة فرعية
  void _showEditSubtaskDialog(Subtask subtask) {
    _titleController.text = subtask.title;
    Get.dialog(
      Dialog(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'تعديل المهمة الفرعية',
                style: AppStyles.titleMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w800,
                  shadows: [
                    Shadow(
                      color: AppColors.getShadowColor(0.2),
                      blurRadius: 1,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _titleController,
                style: TextStyle(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
                decoration: InputDecoration(
                  labelText: 'العنوان *',
                  labelStyle: TextStyle(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.border),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.border),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.primary, width: 2.0),
                  ),
                ),
                maxLength: 100,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: Text('إلغاء', style: TextStyle(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    )),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () async {
                      if (_titleController.text.trim().isEmpty) {
                        Get.snackbar(
                          'خطأ',
                          'يرجى إدخال عنوان للمهمة الفرعية',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                        return;
                      }
                      try {
                        final updatedSubtask = subtask.copyWith(
                          title: _titleController.text.trim(),
                        );
                        final success = await controller.updateSubtask(subtask.id, updatedSubtask);

                        if (success) {
                          await _loadSubtasks();
                          Get.back();
                          Get.snackbar(
                            'نجح',
                            'تم تعديل المهمة الفرعية بنجاح',
                            snackPosition: SnackPosition.BOTTOM,
                          );
                        } else {
                          Get.snackbar(
                            'خطأ',
                            'فشل في تعديل المهمة الفرعية',
                            snackPosition: SnackPosition.BOTTOM,
                          );
                        }
                      } catch (e) {
                        Get.snackbar(
                          'خطأ',
                          'فشل في تعديل المهمة الفرعية',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('حفظ'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض مربع حوار حذف مهمة فرعية
  void _showDeleteSubtaskDialog(Subtask subtask) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف المهمة الفرعية'),
        content: Text('هل أنت متأكد من حذف المهمة الفرعية "${subtask.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              try {
                final success = await controller.deleteSubtask(subtask.id);

                if (success) {
                  await _loadSubtasks();
                  Get.back();
                  Get.snackbar(
                    'نجح',
                    'تم حذف المهمة الفرعية بنجاح',
                    snackPosition: SnackPosition.BOTTOM,
                  );
                } else {
                  Get.back();
                  Get.snackbar(
                    'خطأ',
                    'فشل في حذف المهمة الفرعية',
                    snackPosition: SnackPosition.BOTTOM,
                  );
                }
              } catch (e) {
                Get.back();
                Get.snackbar(
                  'خطأ',
                  'فشل في حذف المهمة الفرعية',
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
